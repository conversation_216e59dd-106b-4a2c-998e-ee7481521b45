directories:
  output: release
  buildResources: build
appId: com.echoscribe.desktop
productName: Echo Scribe Desktop
files:
  - filter:
      - dist/**/*
      - dist-electron/**/*
      - node_modules/**/*
mac:
  category: public.app-category.productivity
  target:
    - target: dmg
      arch:
        - x64
        - arm64
win:
  target:
    - target: nsis
      arch:
        - x64
linux:
  target:
    - target: AppImage
      arch:
        - x64
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
electronVersion: 36.3.1
