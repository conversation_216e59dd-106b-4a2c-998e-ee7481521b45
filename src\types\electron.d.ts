export interface ElectronAPI {
  // File operations
  showSaveDialog: () => Promise<Electron.SaveDialogReturnValue>;
  showOpenDialog: () => Promise<Electron.OpenDialogReturnValue>;
  
  // App info
  getAppVersion: () => Promise<string>;
  
  // Menu events
  onMenuNew: (callback: () => void) => () => void;
  onMenuSave: (callback: () => void) => () => void;
  
  // Platform info
  platform: string;
  
  // Check if running in Electron
  isElectron: boolean;
}

declare global {
  interface Window {
    electronAPI?: ElectronAPI;
  }
  
  const __IS_ELECTRON__: boolean;
}
