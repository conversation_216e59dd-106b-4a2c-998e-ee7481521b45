# Audio Input Setup Guide

This guide explains how to set up different audio input sources for the Enhanced Live Transcription app, including capturing audio from YouTube, media players, and other applications.

## 🎤 Microphone Input (Default)

The simplest option - works with any connected microphone:

1. Connect your microphone (built-in or external)
2. Select the microphone from the audio source dropdown
3. Click "Start Recording"

## 🔊 System Audio Capture (YouTube, Media, etc.)

To capture audio from YouTube, music players, or any other application playing audio, you have several options:

### Option 1: Enable Stereo Mix (Windows)

**Stereo Mix** allows you to record whatever is playing through your speakers as if it were a microphone input.

#### Windows 10/11:
1. Right-click the speaker icon in the system tray
2. Select "Open Sound settings"
3. Click "Sound Control Panel" (or "More sound settings")
4. Go to the "Recording" tab
5. Right-click in the empty space and select "Show Disabled Devices"
6. Find "Stereo Mix" and right-click it
7. Select "Enable"
8. Set it as the default recording device (optional)
9. In the transcription app, select "Stereo Mix" as your audio source

#### If Stereo Mix is not available:
Some audio drivers don't include Stereo Mix. Try updating your audio drivers or use Option 2.

### Option 2: Virtual Audio Cable Software

Virtual audio cables create virtual audio devices that can route audio between applications.

#### Recommended Software:
- **VB-Audio VB-Cable** (Free): https://vb-audio.com/Cable/
- **VoiceMeeter** (Free): https://vb-audio.com/Voicemeeter/
- **Audio Router** (Free): https://github.com/audiorouterdev/audio-router

#### Setup with VB-Cable:
1. Download and install VB-Audio VB-Cable
2. Restart your computer
3. Set "CABLE Input" as your default playback device
4. In the transcription app, select "CABLE Output" as your audio source
5. Play your YouTube video or media
6. Start recording in the transcription app

### Option 3: Browser Screen Capture (Experimental)

The app includes experimental system audio capture using the browser's screen capture API:

1. Select "System Audio (Desktop/Speaker)" from the audio source dropdown
2. Click "Start Recording"
3. Your browser will ask for screen capture permissions
4. Choose to share audio from your entire screen or a specific application
5. The app will attempt to capture the audio

**Note**: This method has limitations and may not work consistently across all browsers and systems.

## 🔧 Troubleshooting

### No Audio Detected
- Check that the correct audio source is selected
- Verify that audio is actually playing (check volume levels)
- Try refreshing the audio devices list
- Check browser permissions for microphone/audio access

### Poor Audio Quality
- Adjust audio levels in Windows sound settings
- Try different audio sources
- For system audio, ensure the source application's volume is adequate

### System Audio Not Working
- Try enabling Stereo Mix first (most reliable method)
- Consider using virtual audio cable software
- Check that your audio drivers are up to date
- Some browsers may block system audio capture

### Browser Compatibility
- Chrome/Chromium: Best support for all features
- Edge: Good support for most features
- Firefox: Limited support for some advanced features
- Safari: Basic support only

## 💡 Tips for Best Results

1. **For YouTube/Media**: Use Stereo Mix or virtual audio cable for most reliable results
2. **For Voice**: Use a dedicated microphone input
3. **Mixed Sources**: You can switch between sources without restarting the app
4. **Audio Levels**: Ensure adequate but not excessive volume levels
5. **Background Noise**: Use microphone inputs with noise suppression when possible

## 🚨 Important Notes

- System audio capture may require additional browser permissions
- Some corporate networks or security software may block audio capture features
- Virtual audio cables may affect your system's audio routing - remember to reset to normal settings when done
- The experimental system audio feature is still in development and may have limitations

## 🆘 Need Help?

If you're having trouble with audio setup:

1. Start with microphone input to verify the app works
2. Try enabling Stereo Mix for system audio
3. Consider virtual audio cable software for advanced setups
4. Check browser console for error messages
5. Ensure all audio drivers are up to date

---

**Remember**: After setting up virtual audio cables or changing system audio settings, you may need to restart your browser or computer for changes to take effect. 