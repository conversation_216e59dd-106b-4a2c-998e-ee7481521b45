# Echo Scribe Desktop - Electron App

This project has been successfully integrated with Electron to create a desktop application for audio transcription.

## 🚀 Quick Start

### Development Mode
```bash
# Start the development server and Electron app
npm run electron:dev
```

### Build Desktop Executable
```bash
# Create production build and desktop executable
npm run dist
```

## 📁 Project Structure

```
echo-scribe-desktop/
├── electron/                 # Electron main process files
│   ├── main.ts              # Main Electron process
│   ├── preload.ts           # Preload script for secure communication
│   └── tsconfig.json        # TypeScript config for Electron
├── src/                     # React application source
├── dist/                    # Built web application
├── dist-electron/           # Compiled Electron files
└── dist-release/            # Desktop executables
    ├── Echo Scribe Desktop Setup 1.0.0.exe  # Windows installer
    └── win-unpacked/        # Unpacked application files
        └── Echo Scribe Desktop.exe          # Direct executable
```

## 🛠️ Available Scripts

| Command | Description |
|---------|-------------|
| `npm run dev` | Start Vite development server |
| `npm run build` | Build web application for production |
| `npm run electron:dev` | Start development server + Electron app |
| `npm run electron:pack` | Build and package Electron app |
| `npm run electron:dist` | Build and create installer |
| `npm run dist` | Complete build and distribution |

## 📦 Generated Files

After running `npm run dist`, you'll find:

1. **`dist-release/Echo Scribe Desktop Setup 1.0.0.exe`** - Windows installer (recommended)
2. **`dist-release/win-unpacked/Echo Scribe Desktop.exe`** - Direct executable

## 🎯 Features

- **Desktop Application**: Native desktop experience with Electron
- **Audio Transcription**: Real-time speech-to-text conversion
- **System Audio Support**: Transcribe system audio (with proper setup)
- **Multiple Engines**: Web Speech API and offline transcription
- **File Operations**: Save and load transcriptions
- **Cross-Platform**: Ready for Windows, macOS, and Linux builds

## 🔧 Configuration

The Electron configuration is in `package.json` under the `"build"` section:

- **App ID**: `com.echoscribe.desktop`
- **Product Name**: Echo Scribe Desktop
- **Output Directory**: `dist-release/`
- **Supported Platforms**: Windows (NSIS), macOS (DMG), Linux (AppImage)

## 🚀 Distribution

### Windows
- **Installer**: `Echo Scribe Desktop Setup 1.0.0.exe`
- **Format**: NSIS installer with custom installation directory option

### Future Platforms
To build for other platforms:

```bash
# macOS (requires macOS)
npm run electron:dist -- --mac

# Linux
npm run electron:dist -- --linux
```

## 🔒 Security

The Electron app is configured with security best practices:
- Context isolation enabled
- Node integration disabled
- Preload script for secure API exposure
- Web security enabled

## 📝 Development Notes

1. **Hot Reload**: Development mode supports hot reload for the React app
2. **DevTools**: Press F12 to open Chrome DevTools in development
3. **Menu**: Application menu with File, Edit, View, Window, and Help options
4. **IPC**: Secure communication between main and renderer processes

## 🎉 Success!

Your Echo Scribe Desktop application is now ready! You can:

1. **Run in development**: `npm run electron:dev`
2. **Create executable**: `npm run dist`
3. **Distribute**: Share the installer or executable with users

The desktop app provides the same functionality as the web version but with enhanced desktop integration and better performance for audio processing tasks.
