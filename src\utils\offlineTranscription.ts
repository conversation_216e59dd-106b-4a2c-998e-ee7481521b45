import { TranscriptionEngine, AudioStreamProcessor } from '@/types/audio';

export interface WhisperConfig {
    modelPath: string;
    modelSize: 'tiny' | 'base' | 'small' | 'medium' | 'large';
    language?: string;
    task?: 'transcribe' | 'translate';
}

export interface VoskConfig {
    modelPath: string;
    sampleRate: number;
    language: string;
}

export class OfflineTranscriptionEngine {
    private worker: Worker | null = null;
    private isInitialized = false;
    private audioContext: AudioContext | null = null;
    private processor: ScriptProcessorNode | null = null;
    private onTranscriptionCallback: ((text: string) => void) | null = null;

    constructor(private config: WhisperConfig | VoskConfig, private engineType: 'whisper' | 'vosk') { }

    async initialize(): Promise<void> {
        if (this.isInitialized) return;

        try {
            // Initialize Web Worker for audio processing
            this.worker = new Worker(new URL('../workers/transcriptionWorker.ts', import.meta.url), {
                type: 'module'
            });

            this.worker.onmessage = (event) => {
                const { type, data } = event.data;

                switch (type) {
                    case 'transcription':
                        if (this.onTranscriptionCallback) {
                            this.onTranscriptionCallback(data.text);
                        }
                        break;
                    case 'error':
                        console.error('Transcription worker error:', data.error);
                        break;
                    case 'initialized':
                        console.log('Transcription engine initialized');
                        break;
                }
            };

            // Initialize the worker with configuration
            this.worker.postMessage({
                type: 'initialize',
                engineType: this.engineType,
                config: this.config
            });

            this.isInitialized = true;
        } catch (error) {
            console.error('Failed to initialize transcription engine:', error);
            throw error;
        }
    }

    async startTranscription(mediaStream: MediaStream, onTranscription: (text: string) => void): Promise<void> {
        if (!this.isInitialized) {
            await this.initialize();
        }

        this.onTranscriptionCallback = onTranscription;

        // Set up audio context and processing
        this.audioContext = new AudioContext({ sampleRate: 16000 });
        const source = this.audioContext.createMediaStreamSource(mediaStream);

        // Create audio processor
        this.processor = this.audioContext.createScriptProcessor(4096, 1, 1);

        this.processor.onaudioprocess = (event) => {
            const inputBuffer = event.inputBuffer;
            const inputData = inputBuffer.getChannelData(0);

            // Convert to 16-bit PCM
            const pcmData = this.convertToPCM16(inputData);

            // Send audio data to worker
            if (this.worker) {
                this.worker.postMessage({
                    type: 'audioData',
                    data: pcmData
                });
            }
        };

        source.connect(this.processor);
        this.processor.connect(this.audioContext.destination);
    }

    stopTranscription(): void {
        if (this.processor) {
            this.processor.disconnect();
            this.processor = null;
        }

        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }

        if (this.worker) {
            this.worker.postMessage({ type: 'stop' });
        }

        this.onTranscriptionCallback = null;
    }

    private convertToPCM16(float32Array: Float32Array): Int16Array {
        const pcm16 = new Int16Array(float32Array.length);
        for (let i = 0; i < float32Array.length; i++) {
            const sample = Math.max(-1, Math.min(1, float32Array[i]));
            pcm16[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
        }
        return pcm16;
    }

    destroy(): void {
        this.stopTranscription();

        if (this.worker) {
            this.worker.terminate();
            this.worker = null;
        }

        this.isInitialized = false;
    }
}

// Factory for creating transcription engines
export class TranscriptionEngineFactory {
    static createWhisperEngine(config: WhisperConfig): OfflineTranscriptionEngine {
        return new OfflineTranscriptionEngine(config, 'whisper');
    }

    static createVoskEngine(config: VoskConfig): OfflineTranscriptionEngine {
        return new OfflineTranscriptionEngine(config, 'vosk');
    }

    static async detectAvailableEngines(): Promise<TranscriptionEngine[]> {
        const engines: TranscriptionEngine[] = [];

        // Web Speech API (always available in supported browsers)
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            engines.push({
                name: 'Web Speech API',
                type: 'web-speech',
                isOnline: true,
                supportsRealtime: true,
                languages: ['en-US', 'en-GB', 'es-ES', 'fr-FR', 'de-DE', 'it-IT', 'pt-BR', 'ru-RU', 'ja-JP', 'ko-KR', 'zh-CN']
            });
        }

        // Check for Whisper.cpp availability
        try {
            // This would check if Whisper models are available
            const whisperAvailable = await TranscriptionEngineFactory.checkWhisperAvailability();
            if (whisperAvailable) {
                engines.push({
                    name: 'Whisper (Offline)',
                    type: 'whisper',
                    isOnline: false,
                    supportsRealtime: true,
                    languages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh', 'ar', 'hi']
                });
            }
        } catch (error) {
            console.warn('Whisper not available:', error);
        }

        // Check for Vosk availability
        try {
            const voskAvailable = await TranscriptionEngineFactory.checkVoskAvailability();
            if (voskAvailable) {
                engines.push({
                    name: 'Vosk (Offline)',
                    type: 'vosk',
                    isOnline: false,
                    supportsRealtime: true,
                    languages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh']
                });
            }
        } catch (error) {
            console.warn('Vosk not available:', error);
        }

        return engines;
    }

    private static async checkWhisperAvailability(): Promise<boolean> {
        // Check if Whisper models are available in public folder or CDN
        try {
            const response = await fetch('/models/whisper/tiny.bin', { method: 'HEAD' });
            return response.ok;
        } catch {
            return false;
        }
    }

    private static async checkVoskAvailability(): Promise<boolean> {
        // Check if Vosk models are available
        try {
            const response = await fetch('/models/vosk/model.tar.gz', { method: 'HEAD' });
            return response.ok;
        } catch {
            return false;
        }
    }
}

// Real-time audio buffer for continuous transcription
export class AudioBuffer {
    private buffer: Float32Array[] = [];
    private maxBufferSize: number;
    private sampleRate: number;

    constructor(maxBufferSizeSeconds: number = 30, sampleRate: number = 16000) {
        this.maxBufferSize = maxBufferSizeSeconds * sampleRate;
        this.sampleRate = sampleRate;
    }

    addAudioData(data: Float32Array): void {
        this.buffer.push(data);

        // Remove old data if buffer is too large
        const totalSamples = this.buffer.reduce((sum, chunk) => sum + chunk.length, 0);
        if (totalSamples > this.maxBufferSize) {
            const excessSamples = totalSamples - this.maxBufferSize;
            let removedSamples = 0;

            while (removedSamples < excessSamples && this.buffer.length > 0) {
                const chunk = this.buffer.shift()!;
                removedSamples += chunk.length;
            }
        }
    }

    getAudioData(): Float32Array {
        if (this.buffer.length === 0) return new Float32Array(0);

        const totalLength = this.buffer.reduce((sum, chunk) => sum + chunk.length, 0);
        const result = new Float32Array(totalLength);

        let offset = 0;
        for (const chunk of this.buffer) {
            result.set(chunk, offset);
            offset += chunk.length;
        }

        return result;
    }

    clear(): void {
        this.buffer = [];
    }

    getDurationSeconds(): number {
        const totalSamples = this.buffer.reduce((sum, chunk) => sum + chunk.length, 0);
        return totalSamples / this.sampleRate;
    }
} 