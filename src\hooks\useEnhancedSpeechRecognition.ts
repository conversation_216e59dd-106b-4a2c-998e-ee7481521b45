import { useState, useRef, useCallback, useEffect } from 'react';
import { AudioInputSource, AudioCaptureOptions } from '@/types/audio';
import { useToast } from '@/hooks/use-toast';

export const useEnhancedSpeechRecognition = () => {
    const [isRecording, setIsRecording] = useState(false);
    const [transcription, setTranscription] = useState('');
    const [interimText, setInterimText] = useState('');
    const [isSupported, setIsSupported] = useState(true);
    const [shouldRestart, setShouldRestart] = useState(false);
    const [consecutiveErrors, setConsecutiveErrors] = useState(0);
    const [currentSource, setCurrentSource] = useState<AudioInputSource | null>(null);

    const recognitionRef = useRef<SpeechRecognition | null>(null);
    const mediaStreamRef = useRef<MediaStream | null>(null);
    const audioContextRef = useRef<AudioContext | null>(null);
    const restartTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const isManualStopRef = useRef(false);
    const lastActivityTimeRef = useRef<number>(Date.now());

    const { toast } = useToast();

    useEffect(() => {
        // Check if Speech Recognition is supported
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        if (!SpeechRecognition) {
            setIsSupported(false);
            toast({
                title: "Speech Recognition Not Supported",
                description: "Your browser doesn't support speech recognition. Please use Chrome or Edge.",
                variant: "destructive",
            });
        }
    }, [toast]);

    const getMediaStream = useCallback(async (source: AudioInputSource): Promise<MediaStream> => {
        const options: AudioCaptureOptions = {
            sourceType: source.type,
            deviceId: source.deviceId,
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
            sampleRate: 44100
        };

        switch (source.type) {
            case 'microphone':
                return await navigator.mediaDevices.getUserMedia({
                    audio: {
                        deviceId: options.deviceId ? { exact: options.deviceId } : undefined,
                        echoCancellation: options.echoCancellation,
                        noiseSuppression: options.noiseSuppression,
                        autoGainControl: options.autoGainControl,
                        sampleRate: options.sampleRate
                    }
                });

            case 'system':
                // System audio is not supported with Web Speech API
                // The Speech Recognition API can only access microphone inputs
                throw new Error('System audio capture is not supported. Please use Stereo Mix or virtual audio cable as microphone input.');

            default:
                throw new Error(`Unsupported source type: ${source.type}`);
        }
    }, []);

    const createRecognition = useCallback(async (source: AudioInputSource, isRestart: boolean = false) => {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        const recognition = new SpeechRecognition();

        recognition.continuous = true;
        recognition.interimResults = true;
        recognition.lang = 'en-US';
        recognition.maxAlternatives = 1;

        console.log('source:', source);

        // For microphone sources, ensure we have proper access
        if (source.type === 'microphone') {
            try {
                // Only test microphone access on initial start, not on restart
                if (!isRestart) {
                    const testStream = await getMediaStream(source);
                    // Check if the stream has active audio tracks
                    const audioTracks = testStream.getAudioTracks();
                    if (audioTracks.length === 0 || !audioTracks[0].enabled) {
                        throw new Error('No active audio tracks available');
                    }
                    // Clean up test stream
                    testStream.getTracks().forEach(track => track.stop());
                }
            } catch (error) {
                console.error('Error accessing microphone:', error);
                throw new Error(`Failed to access microphone: ${error.message}`);
            }
        }

        // For system audio, we need a different approach
        if (source.type === 'system') {
            // System audio has fundamental limitations with Web Speech API
            // The Speech Recognition API can only use microphone inputs, not arbitrary audio streams

            if (!isRestart) {
                // Show user guidance for system audio
                toast({
                    title: "System Audio Setup Required",
                    description: "For system audio transcription, please set 'Stereo Mix' or a virtual audio cable as your default microphone in Windows Sound settings, then use Microphone input instead.",
                    variant: "default",
                });

                // Don't try to set up system audio capture as it won't work with Speech Recognition API
                console.log('System audio selected - user needs to configure Stereo Mix or virtual audio cable');

                // Throw error to prevent starting with system audio
                throw new Error('System audio requires manual setup. Please configure Stereo Mix or virtual audio cable as default microphone, then select Microphone input.');
            } else {
                // For restarts, this shouldn't happen as we prevent system audio from working
                throw new Error('System audio restart not supported - use microphone with Stereo Mix instead');
            }
        }

        recognition.onstart = () => {
            console.log('Speech recognition started');
            // Only set recording state if not manually stopped
            if (!isManualStopRef.current) {
                setIsRecording(true);
                setConsecutiveErrors(0);
                lastActivityTimeRef.current = Date.now();
                if (!shouldRestart) {
                    toast({
                        title: "Recording Started",
                        description: `Listening via ${source.name}...`,
                    });
                }
            }
        };

        recognition.onresult = (event) => {
            console.log('Speech recognition result:');

            // Don't process results if manually stopped
            if (isManualStopRef.current) {
                return;
            }

            lastActivityTimeRef.current = Date.now();
            let finalTranscript = '';
            let interimTranscript = '';

            for (let i = event.resultIndex; i < event.results.length; i++) {
                const transcript = event.results[i][0].transcript;
                if (event.results[i].isFinal) {
                    finalTranscript += transcript + ' ';
                } else {
                    interimTranscript += transcript;
                }
            }

            console.log('Final transcript:', finalTranscript);
            if (finalTranscript) {
                setTranscription(prev => prev + finalTranscript);
            }
            setInterimText(interimTranscript);
        };

        recognition.onerror = (event) => {
            console.error('Speech recognition error:', event.error);

            if (event.error === 'not-allowed') {
                setIsRecording(false);
                setInterimText('');
                isManualStopRef.current = true;
                toast({
                    title: "Recording Error",
                    description: 'Audio access denied. Please allow microphone/audio permissions.',
                    variant: "destructive",
                });
                return;
            }

            if (event.error === 'no-speech') {
                // Don't increment error count for no-speech, it's normal
                console.log('No speech detected, continuing...');
                return;
            }

            // Don't treat aborted errors as real errors during restart
            if (event.error === 'aborted') {
                console.log('Recognition aborted (likely due to restart), continuing...');
                return;
            }

            setConsecutiveErrors(prev => prev + 1);

            // Stop rapid restart loops for certain errors
            if (event.error === 'audio-capture' && consecutiveErrors > 5) {
                console.log('Too many audio capture errors, stopping auto-restart');
                isManualStopRef.current = true;
                setIsRecording(false);
                toast({
                    title: "Audio Capture Error",
                    description: 'Unable to capture audio consistently. Please check your audio source and try again.',
                    variant: "destructive",
                });
                return;
            }

            // Auto-restart for recoverable errors
            if ((event.error === 'network' || event.error === 'audio-capture' || event.error === 'service-not-allowed')
                && shouldRestart && !isManualStopRef.current && consecutiveErrors < 8) {
                console.log(`Attempting to restart recognition due to ${event.error} error (attempt ${consecutiveErrors + 1})`);

                const restartDelay = Math.min(500 + (consecutiveErrors * 200), 2000); // Progressive delay
                restartRecognition(restartDelay);
            }
        };

        recognition.onend = () => {
            console.log('Speech recognition ended, manual stop:', isManualStopRef.current);

            // If this was a manual stop, don't do anything else
            if (isManualStopRef.current) {
                console.log('Manual stop detected, not restarting');
                setInterimText('');
                setIsRecording(false);
                return;
            }

            setInterimText('');

            // For infinite recognition, always restart unless manually stopped
            if (shouldRestart && !isManualStopRef.current) {
                console.log('Auto-restarting recognition for infinite mode...');

                // Progressive delay based on consecutive errors
                let restartDelay = 300; // Reduced base delay for faster restart
                if (consecutiveErrors > 6) {
                    restartDelay = 2000;
                } else if (consecutiveErrors > 4) {
                    restartDelay = 1500;
                } else if (consecutiveErrors > 2) {
                    restartDelay = 1000;
                } else if (consecutiveErrors > 0) {
                    restartDelay = 500;
                }

                restartRecognition(restartDelay);
            } else {
                setIsRecording(false);
            }
        };

        // Add additional event handlers for better lifecycle management
        recognition.onspeechstart = () => {
            console.log('Speech started');
            lastActivityTimeRef.current = Date.now();
        };

        recognition.onspeechend = () => {
            console.log('Speech ended');
            lastActivityTimeRef.current = Date.now();
        };

        recognition.onsoundstart = () => {
            console.log('Sound detected');
            lastActivityTimeRef.current = Date.now();
        };

        recognition.onsoundend = () => {
            console.log('Sound ended');
        };

        recognition.onaudiostart = () => {
            console.log('Audio capture started');
        };

        recognition.onaudioend = () => {
            console.log('Audio capture ended');
        };

        return recognition;
    }, [shouldRestart, toast, consecutiveErrors, getMediaStream]);

    const restartRecognition = useCallback((delay: number = 300) => {
        // Don't restart if manual stop was initiated
        if (isManualStopRef.current) {
            console.log('Manual stop detected, cancelling restart');
            return;
        }

        if (recognitionRef.current) {
            try {
                recognitionRef.current.stop();
            } catch (error) {
                console.log('Error stopping recognition:', error);
            }
        }

        if (restartTimeoutRef.current) {
            clearTimeout(restartTimeoutRef.current);
        }

        restartTimeoutRef.current = setTimeout(async () => {
            // Double-check manual stop before restarting
            if (shouldRestart && !isManualStopRef.current && currentSource) {
                try {
                    console.log('Attempting to restart recognition...');
                    const newRecognition = await createRecognition(currentSource, true); // Pass isRestart = true
                    recognitionRef.current = newRecognition;
                    newRecognition.start();
                    // Reset consecutive errors on successful restart
                    setConsecutiveErrors(0);
                } catch (error) {
                    console.error('Error restarting recognition:', error);

                    // For system audio failures, don't auto-switch as it's not supported
                    if (currentSource.type === 'system') {
                        console.log('System audio failed - stopping recognition as system audio is not supported');
                        isManualStopRef.current = true;
                        setIsRecording(false);
                        toast({
                            title: "System Audio Not Supported",
                            description: "Direct system audio capture is not supported. Please use Stereo Mix or virtual audio cable with Microphone input.",
                            variant: "destructive",
                        });
                        return;
                    } else {
                        // For other errors, try again with exponential backoff
                        setConsecutiveErrors(prev => prev + 1);
                        if (shouldRestart && !isManualStopRef.current && consecutiveErrors < 10) {
                            restartRecognition(Math.min(delay * 1.5, 3000));
                        } else {
                            console.log('Too many restart failures, stopping');
                            isManualStopRef.current = true;
                            setIsRecording(false);
                        }
                    }
                }
            } else {
                console.log('Restart cancelled - shouldRestart:', shouldRestart, 'manualStop:', isManualStopRef.current, 'hasSource:', !!currentSource);
            }
        }, delay);
    }, [createRecognition, shouldRestart, consecutiveErrors, currentSource, toast]);

    const startRecording = useCallback(async (source: AudioInputSource) => {
        if (!isSupported) {
            toast({
                title: "Not Supported",
                description: "Speech recognition is not supported in this browser.",
                variant: "destructive",
            });
            return;
        }

        console.log('Starting infinite recognition with source:', source.name);

        // Reset all states for fresh start
        isManualStopRef.current = false;
        setShouldRestart(true);
        setConsecutiveErrors(0);
        setCurrentSource(source);
        lastActivityTimeRef.current = Date.now();

        // Clear any existing timeouts
        if (restartTimeoutRef.current) {
            clearTimeout(restartTimeoutRef.current);
            restartTimeoutRef.current = null;
        }

        // Stop any existing recognition
        if (recognitionRef.current) {
            try {
                recognitionRef.current.stop();
            } catch (error) {
                console.log('Error stopping existing recognition:', error);
            }
        }

        try {
            const recognition = await createRecognition(source, false); // Initial start, not restart
            recognitionRef.current = recognition;
            recognition.start();

            toast({
                title: "Infinite Recording Started",
                description: `Continuous transcription from ${source.name}. Click Stop to end.`,
                variant: "default",
            });
        } catch (error) {
            console.error('Error starting recognition:', error);
            isManualStopRef.current = true;
            setShouldRestart(false);
            setIsRecording(false);
            toast({
                title: "Recording Error",
                description: `Failed to start recording from ${source.name}. Please check permissions and try again.`,
                variant: "destructive",
            });
        }
    }, [isSupported, createRecognition, toast]);

    const stopRecording = useCallback(() => {
        console.log('Manual stop recording initiated');
        isManualStopRef.current = true;
        setShouldRestart(false);
        setConsecutiveErrors(0);
        setCurrentSource(null);
        setIsRecording(false);
        setInterimText('');

        if (restartTimeoutRef.current) {
            clearTimeout(restartTimeoutRef.current);
            restartTimeoutRef.current = null;
        }

        // Clean up any remaining resources (though system audio is no longer supported)
        if (mediaStreamRef.current) {
            mediaStreamRef.current.getTracks().forEach(track => track.stop());
            mediaStreamRef.current = null;
        }
        if (audioContextRef.current) {
            audioContextRef.current.close();
            audioContextRef.current = null;
        }

        if (recognitionRef.current) {
            try {
                recognitionRef.current.stop();
                recognitionRef.current = null;
            } catch (error) {
                console.log('Error stopping recognition:', error);
            }
        }

        toast({
            title: "Recording Stopped",
            description: "Transcription saved.",
        });
    }, [toast]);

    const clearTranscription = useCallback(() => {
        setTranscription('');
        setInterimText('');
        toast({
            title: "Transcription Cleared",
            description: "Text has been reset.",
        });
    }, [toast]);

    const resetErrors = useCallback(() => {
        setConsecutiveErrors(0);
        isManualStopRef.current = false;
        toast({
            title: "Errors Reset",
            description: "Audio error count has been reset. You can try recording again.",
        });
    }, [toast]);

    // Monitor for extended periods without activity and restart if needed
    useEffect(() => {
        if (!shouldRestart || isManualStopRef.current || !isRecording) return;

        const activityCheckInterval = setInterval(() => {
            // Don't restart if manual stop was initiated or not recording
            if (isManualStopRef.current || !isRecording) {
                return;
            }

            const timeSinceLastActivity = Date.now() - lastActivityTimeRef.current;

            // Only restart if no activity for 15 seconds and not too many errors
            if (timeSinceLastActivity > 15000 && consecutiveErrors < 5 && shouldRestart) {
                console.log('No activity detected for 15 seconds, restarting recognition...');
                restartRecognition(500);
            }
        }, 3000); // Check every 3 seconds

        return () => clearInterval(activityCheckInterval);
    }, [shouldRestart, isRecording, restartRecognition, consecutiveErrors]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (restartTimeoutRef.current) {
                clearTimeout(restartTimeoutRef.current);
            }
            if (mediaStreamRef.current) {
                mediaStreamRef.current.getTracks().forEach(track => track.stop());
            }
            if (audioContextRef.current) {
                audioContextRef.current.close();
            }
            if (recognitionRef.current) {
                try {
                    recognitionRef.current.stop();
                } catch (error) {
                    console.log('Cleanup error:', error);
                }
            }
        };
    }, []);

    return {
        isRecording,
        transcription,
        interimText,
        isSupported,
        consecutiveErrors,
        currentSource,
        startRecording,
        stopRecording,
        clearTranscription,
        resetErrors
    };
}; 