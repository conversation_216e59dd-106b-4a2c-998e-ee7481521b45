import { SystemAudioCapture, AudioInputSource, AudioCapabilities } from '@/types/audio';

export class SystemAudioDetector {
    private static instance: SystemAudioDetector;
    private capabilities: SystemAudioCapture | null = null;

    static getInstance(): SystemAudioDetector {
        if (!SystemAudioDetector.instance) {
            SystemAudioDetector.instance = new SystemAudioDetector();
        }
        return SystemAudioDetector.instance;
    }

    async detectSystemAudioCapabilities(): Promise<SystemAudioCapture> {
        if (this.capabilities) {
            return this.capabilities;
        }

        const capabilities: SystemAudioCapture = {
            isSupported: false,
            method: 'none',
            quality: 'low',
            latency: 1000
        };

        // Check for getDisplayMedia support (Chrome/Edge)
        if (this.supportsGetDisplayMedia()) {
            capabilities.isSupported = true;
            capabilities.method = 'getDisplayMedia';
            capabilities.quality = 'medium';
            capabilities.latency = 200;
        }

        // Check for Stereo Mix availability
        const stereoMixAvailable = await this.checkStereoMixAvailability();
        if (stereoMixAvailable) {
            capabilities.isSupported = true;
            capabilities.method = 'stereoMix';
            capabilities.quality = 'high';
            capabilities.latency = 50;
        }

        // Check for virtual audio cable
        const virtualCableAvailable = await this.checkVirtualAudioCable();
        if (virtualCableAvailable) {
            capabilities.isSupported = true;
            capabilities.method = 'virtualCable';
            capabilities.quality = 'high';
            capabilities.latency = 30;
        }

        this.capabilities = capabilities;
        return capabilities;
    }

    private supportsGetDisplayMedia(): boolean {
        return !!(navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia);
    }

    private async checkStereoMixAvailability(): Promise<boolean> {
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();
            return devices.some(device =>
                device.kind === 'audioinput' &&
                (device.label.toLowerCase().includes('stereo mix') ||
                    device.label.toLowerCase().includes('what u hear') ||
                    device.label.toLowerCase().includes('wave out mix'))
            );
        } catch (error) {
            console.warn('Could not check for Stereo Mix:', error);
            return false;
        }
    }

    private async checkVirtualAudioCable(): Promise<boolean> {
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();
            return devices.some(device =>
                device.kind === 'audioinput' &&
                (device.label.toLowerCase().includes('cable') ||
                    device.label.toLowerCase().includes('vb-audio') ||
                    device.label.toLowerCase().includes('voicemeeter') ||
                    device.label.toLowerCase().includes('virtual'))
            );
        } catch (error) {
            console.warn('Could not check for virtual audio cable:', error);
            return false;
        }
    }

    async getEnhancedAudioSources(): Promise<AudioInputSource[]> {
        const sources: AudioInputSource[] = [];

        try {
            // Request permissions first
            await navigator.mediaDevices.getUserMedia({ audio: true });

            const devices = await navigator.mediaDevices.enumerateDevices();
            const audioInputDevices = devices.filter(device => device.kind === 'audioinput');

            // Add regular microphones
            for (const device of audioInputDevices) {
                const capabilities = this.analyzeDeviceCapabilities(device);

                sources.push({
                    id: device.deviceId,
                    name: device.label || `Microphone ${device.deviceId.slice(0, 8)}`,
                    type: this.categorizeDevice(device),
                    deviceId: device.deviceId,
                    capabilities
                });
            }

            // Add system audio options based on capabilities
            const systemCapabilities = await this.detectSystemAudioCapabilities();

            if (systemCapabilities.method === 'getDisplayMedia') {
                sources.push({
                    id: 'system-display-media',
                    name: 'System Audio (Screen Capture)',
                    type: 'system',
                    capabilities: {
                        supportsSystemAudio: true,
                        supportsLoopback: false,
                        requiresPermission: true,
                        quality: 'medium'
                    }
                });
            }

        } catch (error) {
            console.error('Error getting enhanced audio sources:', error);
        }

        return sources;
    }

    private analyzeDeviceCapabilities(device: MediaDeviceInfo): AudioCapabilities {
        const label = device.label.toLowerCase();

        // Detect system audio devices
        const isSystemAudio = label.includes('stereo mix') ||
            label.includes('what u hear') ||
            label.includes('wave out mix');

        // Detect virtual audio cables
        const isVirtualCable = label.includes('cable') ||
            label.includes('vb-audio') ||
            label.includes('voicemeeter') ||
            label.includes('virtual');

        return {
            supportsSystemAudio: isSystemAudio || isVirtualCable,
            supportsLoopback: isSystemAudio,
            requiresPermission: !isSystemAudio && !isVirtualCable,
            quality: isSystemAudio ? 'high' : isVirtualCable ? 'high' : 'medium'
        };
    }

    private categorizeDevice(device: MediaDeviceInfo): AudioInputSource['type'] {
        const label = device.label.toLowerCase();

        if (label.includes('stereo mix') || label.includes('what u hear')) {
            return 'loopback';
        }

        if (label.includes('cable') || label.includes('vb-audio') || label.includes('voicemeeter')) {
            return 'virtual';
        }

        return 'microphone';
    }

    async captureSystemAudio(): Promise<MediaStream> {
        const capabilities = await this.detectSystemAudioCapabilities();

        switch (capabilities.method) {
            case 'getDisplayMedia':
                return this.captureViaDisplayMedia();
            case 'stereoMix':
                return this.captureViaStereoMix();
            case 'virtualCable':
                return this.captureViaVirtualCable();
            default:
                throw new Error('No system audio capture method available');
        }
    }

    private async captureViaDisplayMedia(): Promise<MediaStream> {
        try {
            const stream = await navigator.mediaDevices.getDisplayMedia({
                video: false,
                audio: {
                    echoCancellation: false,
                    noiseSuppression: false,
                    autoGainControl: false,
                    sampleRate: 48000
                }
            });

            // Ensure we have audio tracks
            const audioTracks = stream.getAudioTracks();
            if (audioTracks.length === 0) {
                throw new Error('No audio tracks in display media stream');
            }

            return stream;
        } catch (error) {
            console.error('Failed to capture via getDisplayMedia:', error);
            throw new Error('Screen audio capture failed. Please ensure you select "Share audio" when prompted.');
        }
    }

    private async captureViaStereoMix(): Promise<MediaStream> {
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();
            const stereoMixDevice = devices.find(device =>
                device.kind === 'audioinput' &&
                device.label.toLowerCase().includes('stereo mix')
            );

            if (!stereoMixDevice) {
                throw new Error('Stereo Mix device not found');
            }

            return await navigator.mediaDevices.getUserMedia({
                audio: {
                    deviceId: { exact: stereoMixDevice.deviceId },
                    echoCancellation: false,
                    noiseSuppression: false,
                    autoGainControl: false,
                    sampleRate: 48000
                }
            });
        } catch (error) {
            console.error('Failed to capture via Stereo Mix:', error);
            throw new Error('Stereo Mix capture failed. Please ensure Stereo Mix is enabled in Windows Sound settings.');
        }
    }

    private async captureViaVirtualCable(): Promise<MediaStream> {
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();
            const virtualDevice = devices.find(device =>
                device.kind === 'audioinput' &&
                (device.label.toLowerCase().includes('cable') ||
                    device.label.toLowerCase().includes('vb-audio'))
            );

            if (!virtualDevice) {
                throw new Error('Virtual audio cable device not found');
            }

            return await navigator.mediaDevices.getUserMedia({
                audio: {
                    deviceId: { exact: virtualDevice.deviceId },
                    echoCancellation: false,
                    noiseSuppression: false,
                    autoGainControl: false,
                    sampleRate: 48000
                }
            });
        } catch (error) {
            console.error('Failed to capture via virtual cable:', error);
            throw new Error('Virtual audio cable capture failed. Please ensure virtual audio cable software is installed and configured.');
        }
    }

    generateSetupInstructions(): string[] {
        const instructions: string[] = [];

        if (this.supportsGetDisplayMedia()) {
            instructions.push(
                "🖥️ Browser Screen Capture:",
                "• Click 'Start Recording' and select 'Share audio'",
                "• Choose the application or entire screen",
                "• Works with Chrome/Edge browsers",
                ""
            );
        }

        instructions.push(
            "🔊 Stereo Mix Setup (Recommended):",
            "• Right-click speaker icon → Open Sound settings",
            "• Go to Sound Control Panel → Recording tab",
            "• Right-click empty space → Show Disabled Devices",
            "• Enable 'Stereo Mix' and set as default",
            "• Select 'Stereo Mix' as microphone input",
            "",
            "🎛️ Virtual Audio Cable (Advanced):",
            "• Install VB-Audio VB-Cable or VoiceMeeter",
            "• Set virtual cable as default playback device",
            "• Select virtual cable output as microphone input",
            "• Provides highest quality system audio capture"
        );

        return instructions;
    }
} 