import { execSync } from 'child_process';
import { rmSync, existsSync } from 'fs';
import path from 'path';

console.log('🧹 Starting clean build process...');

// Function to safely remove directory
function safeRemove(dirPath) {
  try {
    if (existsSync(dirPath)) {
      console.log(`🗑️  Removing ${dirPath}...`);
      rmSync(dirPath, { recursive: true, force: true });
      console.log(`✅ Successfully removed ${dirPath}`);
    }
  } catch (error) {
    console.warn(`⚠️  Warning: Could not remove ${dirPath}: ${error.message}`);
  }
}

// Function to run command safely
function runCommand(command, description) {
  try {
    console.log(`🔨 ${description}...`);
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} completed`);
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    process.exit(1);
  }
}

// Clean previous builds
console.log('🧹 Cleaning previous builds...');
safeRemove('dist');
safeRemove('dist-electron');
safeRemove('release');

// Wait a moment for file system
await new Promise(resolve => setTimeout(resolve, 1000));

// Build web application
runCommand('npm run build', 'Building web application');

// Build Electron main process
runCommand('npm run build:electron', 'Building Electron main process');

// Build desktop application
runCommand('electron-builder build --win', 'Building desktop application');

console.log('🎉 Clean build completed successfully!');
console.log('📦 Check the release/ directory for your executable files.');
