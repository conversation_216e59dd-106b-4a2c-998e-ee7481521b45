import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, <PERSON>, Trash2, <PERSON>ting<PERSON>, RefreshCw, <PERSON>pu, Wifi, Volume2, Monitor } from 'lucide-react';
import ControlButton from './ControlButton';
import TranscriptionDisplay from './TranscriptionDisplay';
import { useToast } from '@/hooks/use-toast';
import { useSystemAudioTranscription } from '@/hooks/useSystemAudioTranscription';
import { AudioInputSource, TranscriptionEngine } from '@/types/audio';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';

const SystemAudioTranscriptionApp = () => {
    const [showSettings, setShowSettings] = useState(false);
    const [showEngineSettings, setShowEngineSettings] = useState(false);
    const [availableSources, setAvailableSources] = useState<AudioInputSource[]>([]);
    const [selectedSource, setSelectedSource] = useState<AudioInputSource | null>(null);

    const {
        isRecording,
        transcription,
        interimText,
        currentSource,
        currentEngine,
        availableEngines,
        systemCapabilities,
        isInitializing,
        audioLevel,
        startRecording,
        stopRecording,
        clearTranscription,
        switchEngine,
        getEnhancedAudioSources,
        getSetupInstructions,
        isSystemAudioSupported,
        hasOfflineEngines,
        recommendedSetup
    } = useSystemAudioTranscription({
        preferredEngine: 'web-speech',
        enableOfflineMode: true,
        language: 'en-US'
    });

    const { toast } = useToast();

    // Load available audio sources
    useEffect(() => {
        const loadSources = async () => {
            try {
                const sources = await getEnhancedAudioSources();
                setAvailableSources(sources);

                // Auto-select the best system audio source if available
                const systemAudioSource = sources.find(s =>
                    s.capabilities?.supportsSystemAudio && s.capabilities.quality === 'high'
                );
                setSelectedSource(systemAudioSource || sources[0] || null);
            } catch (error) {
                console.error('Failed to load audio sources:', error);
                toast({
                    title: "Audio Sources Error",
                    description: "Failed to detect audio sources. Please check permissions.",
                    variant: "destructive",
                });
            }
        };

        if (!isInitializing) {
            loadSources();
        }
    }, [isInitializing, getEnhancedAudioSources, toast]);

    const handleStartRecording = () => {
        if (!selectedSource) {
            toast({
                title: "No Audio Source Selected",
                description: "Please select an audio input source before starting recording.",
                variant: "destructive",
            });
            return;
        }

        if (!currentEngine) {
            toast({
                title: "No Transcription Engine",
                description: "Please wait for transcription engines to initialize.",
                variant: "destructive",
            });
            return;
        }

        startRecording(selectedSource, currentEngine);
    };

    const handleSourceSelect = (sourceId: string) => {
        const source = availableSources.find(s => s.id === sourceId);
        if (source) {
            setSelectedSource(source);
            if (isRecording) {
                toast({
                    title: "Audio Source Changed",
                    description: "Stop and restart recording to use the new audio source.",
                    variant: "default",
                });
            }
        }
    };

    const handleEngineSelect = (engineName: string) => {
        const engine = availableEngines.find(e => e.name === engineName);
        if (engine) {
            switchEngine(engine);
        }
    };

    const getSourceIcon = (type: string) => {
        switch (type) {
            case 'microphone':
                return <Mic size={16} />;
            case 'system':
            case 'loopback':
                return <Monitor size={16} />;
            case 'virtual':
                return <Volume2 size={16} />;
            default:
                return <Mic size={16} />;
        }
    };

    const getSourceDescription = (source: AudioInputSource) => {
        if (source.capabilities?.supportsSystemAudio) {
            return `System Audio (${source.capabilities.quality} quality)`;
        }
        switch (source.type) {
            case 'microphone':
                return 'Microphone input';
            case 'system':
                return 'System audio capture';
            case 'loopback':
                return 'System audio loopback';
            case 'virtual':
                return 'Virtual audio cable';
            default:
                return 'Audio input';
        }
    };

    const getEngineIcon = (engine: TranscriptionEngine) => {
        return engine.isOnline ? <Wifi size={16} /> : <Cpu size={16} />;
    };

    if (isInitializing) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4">
                <div className="bg-white rounded-2xl shadow-xl p-8 text-center max-w-md">
                    <div className="text-blue-500 mb-4">
                        <RefreshCw size={48} className="mx-auto animate-spin" />
                    </div>
                    <h2 className="text-2xl font-bold text-gray-800 mb-4">Initializing</h2>
                    <p className="text-gray-600">
                        Detecting system audio capabilities and transcription engines...
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-4">
            <div className="max-w-4xl mx-auto">
                {/* Header */}
                <div className="text-center mb-8">
                    <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
                        System Audio Transcription
                    </h1>
                    <p className="text-gray-600 text-lg">
                        Real-time transcription of system audio, YouTube, meetings, and more
                    </p>
                </div>

                {/* System Status */}
                <div className="bg-white rounded-2xl shadow-xl p-6 mb-6">
                    <h2 className="text-lg font-semibold text-gray-800 mb-4">System Status</h2>
                    <div className="grid md:grid-cols-3 gap-4 text-sm">
                        <div className="flex items-center gap-2">
                            <div className={`w-3 h-3 rounded-full ${isSystemAudioSupported ? 'bg-green-500' : 'bg-red-500'}`}></div>
                            <span>System Audio: {isSystemAudioSupported ? 'Supported' : 'Not Available'}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <div className={`w-3 h-3 rounded-full ${hasOfflineEngines ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
                            <span>Offline Engines: {hasOfflineEngines ? 'Available' : 'Online Only'}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <div className={`w-3 h-3 rounded-full ${recommendedSetup !== 'none' ? 'bg-green-500' : 'bg-orange-500'}`}></div>
                            <span>Setup: {recommendedSetup === 'none' ? 'Manual Required' : recommendedSetup}</span>
                        </div>
                    </div>
                </div>

                {/* Audio Source Settings */}
                <div className="bg-white rounded-2xl shadow-xl p-6 mb-6">
                    <div className="flex items-center justify-between mb-4">
                        <h2 className="text-lg font-semibold text-gray-800">Audio Source</h2>
                        <ControlButton
                            onClick={() => setShowSettings(!showSettings)}
                            variant="secondary"
                            icon={<Settings size={16} />}
                        >
                            {showSettings ? 'Hide' : 'Show'} Settings
                        </ControlButton>
                    </div>

                    {showSettings && (
                        <div className="border-t pt-4 space-y-4">
                            <div>
                                <label className="text-sm font-medium text-gray-700 mb-2 block">
                                    Audio Input Source
                                </label>
                                <Select
                                    value={selectedSource?.id || ''}
                                    onValueChange={handleSourceSelect}
                                    disabled={isRecording}
                                >
                                    <SelectTrigger className="w-full">
                                        <SelectValue placeholder="Select audio source" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {availableSources.map((source) => (
                                            <SelectItem key={source.id} value={source.id}>
                                                <div className="flex items-center gap-2">
                                                    {getSourceIcon(source.type)}
                                                    <div className="flex flex-col">
                                                        <span className="font-medium">{source.name}</span>
                                                        <span className="text-xs text-gray-500">
                                                            {getSourceDescription(source)}
                                                        </span>
                                                    </div>
                                                </div>
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div>
                                <label className="text-sm font-medium text-gray-700 mb-2 block">
                                    Transcription Engine
                                </label>
                                <Select
                                    value={currentEngine?.name || ''}
                                    onValueChange={handleEngineSelect}
                                    disabled={isRecording}
                                >
                                    <SelectTrigger className="w-full">
                                        <SelectValue placeholder="Select transcription engine" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {availableEngines.map((engine) => (
                                            <SelectItem key={engine.name} value={engine.name}>
                                                <div className="flex items-center gap-2">
                                                    {getEngineIcon(engine)}
                                                    <div className="flex flex-col">
                                                        <span className="font-medium">{engine.name}</span>
                                                        <span className="text-xs text-gray-500">
                                                            {engine.isOnline ? 'Online' : 'Offline'} • {engine.languages.slice(0, 3).join(', ')}
                                                        </span>
                                                    </div>
                                                </div>
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    )}

                    {!showSettings && selectedSource && (
                        <div className="text-sm text-gray-600">
                            <div className="flex items-center gap-2 mb-1">
                                {getSourceIcon(selectedSource.type)}
                                <span className="font-medium">Source:</span> {selectedSource.name}
                            </div>
                            {currentEngine && (
                                <div className="flex items-center gap-2">
                                    {getEngineIcon(currentEngine)}
                                    <span className="font-medium">Engine:</span> {currentEngine.name}
                                </div>
                            )}
                        </div>
                    )}
                </div>

                {/* Control Panel */}
                <div className="bg-white rounded-2xl shadow-xl p-6 mb-6">
                    <div className="flex items-center justify-center gap-4">
                        <ControlButton
                            onClick={handleStartRecording}
                            disabled={isRecording || !selectedSource || !currentEngine}
                            variant="primary"
                            icon={<Mic size={20} />}
                        >
                            Start Recording
                        </ControlButton>

                        <ControlButton
                            onClick={stopRecording}
                            disabled={!isRecording}
                            variant="secondary"
                            icon={<Square size={20} />}
                        >
                            Stop Recording
                        </ControlButton>

                        <ControlButton
                            onClick={clearTranscription}
                            disabled={isRecording}
                            variant="danger"
                            icon={<Trash2 size={20} />}
                        >
                            Clear
                        </ControlButton>
                    </div>

                    {/* Recording Status */}
                    {isRecording && (
                        <div className="mt-4 text-center">
                            <div className="flex items-center justify-center gap-2 text-green-600 mb-2">
                                <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                                <span className="font-medium">
                                    Recording from {currentSource?.name || 'Unknown source'}
                                </span>
                            </div>

                            {/* Audio Level Indicator */}
                            <div className="w-full max-w-xs mx-auto bg-gray-200 rounded-full h-2">
                                <div
                                    className="bg-green-500 h-2 rounded-full transition-all duration-100"
                                    style={{ width: `${Math.min(audioLevel * 100, 100)}%` }}
                                ></div>
                            </div>
                            <span className="text-xs text-gray-500 mt-1 block">
                                Audio Level: {Math.round(audioLevel * 100)}%
                            </span>
                        </div>
                    )}

                    {!selectedSource && !isRecording && (
                        <div className="mt-4 text-center text-amber-600 bg-amber-50 rounded-lg p-3">
                            <p className="text-sm font-medium">⚠️ No audio source selected</p>
                            <p className="text-xs mt-1">Please select an audio input source to begin recording</p>
                        </div>
                    )}
                </div>

                {/* Transcription Display */}
                <TranscriptionDisplay
                    transcription={transcription}
                    interimText={interimText}
                    isRecording={isRecording}
                />

                {/* Setup Instructions */}
                <div className="mt-6 bg-white rounded-2xl shadow-xl p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">🎯 System Audio Setup Guide</h3>

                    <div className="grid md:grid-cols-2 gap-6 text-sm text-gray-600">
                        <div className="space-y-3">
                            <h4 className="font-medium text-gray-800">🔊 For YouTube/Media (Recommended):</h4>
                            <ol className="list-decimal list-inside space-y-1">
                                <li>Right-click speaker icon → Sound settings</li>
                                <li>Go to "Sound Control Panel" → Recording tab</li>
                                <li>Right-click empty space → "Show Disabled Devices"</li>
                                <li>Enable "Stereo Mix" and set as default</li>
                                <li>Select "Stereo Mix" as your audio source above</li>
                            </ol>
                        </div>

                        <div className="space-y-3">
                            <h4 className="font-medium text-gray-800">🎛️ Advanced Setup (VB-Cable):</h4>
                            <ol className="list-decimal list-inside space-y-1">
                                <li>Download and install VB-Audio VB-Cable</li>
                                <li>Set "CABLE Input" as default playback device</li>
                                <li>Select "CABLE Output" as audio source above</li>
                                <li>Play your media and start recording</li>
                                <li>Provides highest quality system audio capture</li>
                            </ol>
                        </div>
                    </div>

                    <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                        <p className="text-xs text-blue-700">
                            <strong>💡 Pro Tip:</strong> For best results with video calls (Zoom, Meet), use VB-Cable or enable "Stereo Mix"
                            to capture both your voice and other participants. The app will transcribe all audio playing through your speakers.
                        </p>
                    </div>

                    {systemCapabilities && (
                        <div className="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">
                            <p className="text-xs text-green-700">
                                <strong>✅ Detected Setup:</strong> {systemCapabilities.method}
                                ({systemCapabilities.quality} quality, ~{systemCapabilities.latency}ms latency)
                            </p>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default SystemAudioTranscriptionApp; 