import { useState, useRef, useCallback, useEffect } from 'react';
import { AudioInputSource, TranscriptionEngine, SystemAudioCapture } from '@/types/audio';
import { SystemAudioDetector } from '@/utils/systemAudioDetector';
import { OfflineTranscriptionEngine, TranscriptionEngineFactory, WhisperConfig, VoskConfig } from '@/utils/offlineTranscription';
import { useToast } from '@/hooks/use-toast';

export interface SystemAudioTranscriptionOptions {
    preferredEngine?: 'web-speech' | 'whisper' | 'vosk';
    enableOfflineMode?: boolean;
    bufferDuration?: number;
    language?: string;
}

export const useSystemAudioTranscription = (options: SystemAudioTranscriptionOptions = {}) => {
    const [isRecording, setIsRecording] = useState(false);
    const [transcription, setTranscription] = useState('');
    const [interimText, setInterimText] = useState('');
    const [currentSource, setCurrentSource] = useState<AudioInputSource | null>(null);
    const [currentEngine, setCurrentEngine] = useState<TranscriptionEngine | null>(null);
    const [availableEngines, setAvailableEngines] = useState<TranscriptionEngine[]>([]);
    const [systemCapabilities, setSystemCapabilities] = useState<SystemAudioCapture | null>(null);
    const [isInitializing, setIsInitializing] = useState(false);
    const [audioLevel, setAudioLevel] = useState(0);

    const recognitionRef = useRef<SpeechRecognition | null>(null);
    const offlineEngineRef = useRef<OfflineTranscriptionEngine | null>(null);
    const mediaStreamRef = useRef<MediaStream | null>(null);
    const audioContextRef = useRef<AudioContext | null>(null);
    const analyserRef = useRef<AnalyserNode | null>(null);
    const isManualStopRef = useRef(false);
    const systemDetectorRef = useRef<SystemAudioDetector>(SystemAudioDetector.getInstance());

    const { toast } = useToast();

    // Initialize system capabilities and engines
    useEffect(() => {
        const initializeCapabilities = async () => {
            setIsInitializing(true);

            try {
                // Detect system audio capabilities
                const capabilities = await systemDetectorRef.current.detectSystemAudioCapabilities();
                setSystemCapabilities(capabilities);

                // Detect available transcription engines
                const engines = await TranscriptionEngineFactory.detectAvailableEngines();
                setAvailableEngines(engines);

                // Set default engine based on preferences
                const preferredEngine = engines.find(e => e.type === options.preferredEngine) || engines[0];
                if (preferredEngine) {
                    setCurrentEngine(preferredEngine);
                }

                console.log('System capabilities:', capabilities);
                console.log('Available engines:', engines);

            } catch (error) {
                console.error('Failed to initialize capabilities:', error);
                toast({
                    title: "Initialization Error",
                    description: "Failed to detect system audio capabilities.",
                    variant: "destructive",
                });
            } finally {
                setIsInitializing(false);
            }
        };

        initializeCapabilities();
    }, [options.preferredEngine, toast]);

    // Audio level monitoring
    const updateAudioLevel = useCallback(() => {
        if (!analyserRef.current) return;

        const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
        analyserRef.current.getByteFrequencyData(dataArray);

        const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
        setAudioLevel(average / 255);

        if (isRecording) {
            requestAnimationFrame(updateAudioLevel);
        }
    }, [isRecording]);

    // Enhanced audio source detection
    const getEnhancedAudioSources = useCallback(async (): Promise<AudioInputSource[]> => {
        try {
            return await systemDetectorRef.current.getEnhancedAudioSources();
        } catch (error) {
            console.error('Failed to get audio sources:', error);
            return [];
        }
    }, []);

    // Start recording with enhanced system audio support
    const startRecording = useCallback(async (source: AudioInputSource, engine?: TranscriptionEngine) => {
        if (isRecording) return;

        isManualStopRef.current = false;
        setIsRecording(true);
        setCurrentSource(source);

        const selectedEngine = engine || currentEngine;
        if (!selectedEngine) {
            toast({
                title: "No Engine Selected",
                description: "Please select a transcription engine.",
                variant: "destructive",
            });
            setIsRecording(false);
            return;
        }

        try {
            // Capture audio based on source type and capabilities
            let mediaStream: MediaStream;

            if (source.capabilities?.supportsSystemAudio) {
                // Use enhanced system audio capture
                mediaStream = await systemDetectorRef.current.captureSystemAudio();
            } else if (source.type === 'system') {
                // Fallback to display media for system audio
                mediaStream = await navigator.mediaDevices.getDisplayMedia({
                    video: false,
                    audio: {
                        echoCancellation: false,
                        noiseSuppression: false,
                        autoGainControl: false,
                        sampleRate: 48000
                    }
                });
            } else {
                // Regular microphone capture
                mediaStream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        deviceId: source.deviceId ? { exact: source.deviceId } : undefined,
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true,
                        sampleRate: 48000
                    }
                });
            }

            mediaStreamRef.current = mediaStream;

            // Set up audio analysis
            audioContextRef.current = new AudioContext();
            const sourceNode = audioContextRef.current.createMediaStreamSource(mediaStream);
            analyserRef.current = audioContextRef.current.createAnalyser();
            analyserRef.current.fftSize = 256;
            sourceNode.connect(analyserRef.current);

            // Start audio level monitoring
            updateAudioLevel();

            // Initialize transcription based on engine type
            if (selectedEngine.type === 'web-speech') {
                await startWebSpeechRecognition(selectedEngine);
            } else {
                await startOfflineTranscription(mediaStream, selectedEngine);
            }

            toast({
                title: "Recording Started",
                description: `Transcribing ${source.name} with ${selectedEngine.name}`,
                variant: "default",
            });

        } catch (error) {
            console.error('Failed to start recording:', error);
            setIsRecording(false);
            toast({
                title: "Recording Failed",
                description: `Failed to start recording: ${error.message}`,
                variant: "destructive",
            });
        }
    }, [isRecording, currentEngine, toast, updateAudioLevel]);

    // Web Speech API implementation
    const startWebSpeechRecognition = useCallback(async (engine: TranscriptionEngine) => {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        const recognition = new SpeechRecognition();

        recognition.continuous = true;
        recognition.interimResults = true;
        recognition.lang = options.language || 'en-US';
        recognition.maxAlternatives = 1;

        recognition.onresult = (event) => {
            if (isManualStopRef.current) return;

            let finalTranscript = '';
            let interimTranscript = '';

            for (let i = event.resultIndex; i < event.results.length; i++) {
                const transcript = event.results[i][0].transcript;
                if (event.results[i].isFinal) {
                    finalTranscript += transcript + ' ';
                } else {
                    interimTranscript += transcript;
                }
            }

            if (finalTranscript) {
                setTranscription(prev => prev + finalTranscript);
            }
            setInterimText(interimTranscript);
        };

        recognition.onerror = (event) => {
            console.error('Speech recognition error:', event.error);
            if (event.error === 'not-allowed') {
                toast({
                    title: "Permission Denied",
                    description: "Microphone access was denied.",
                    variant: "destructive",
                });
                stopRecording();
            }
        };

        recognition.onend = () => {
            if (!isManualStopRef.current && isRecording) {
                // Auto-restart for continuous recognition
                setTimeout(() => {
                    if (!isManualStopRef.current && isRecording) {
                        recognition.start();
                    }
                }, 100);
            }
        };

        recognitionRef.current = recognition;
        recognition.start();
    }, [options.language, isRecording, toast]);

    // Offline transcription implementation
    const startOfflineTranscription = useCallback(async (mediaStream: MediaStream, engine: TranscriptionEngine) => {
        try {
            let config: WhisperConfig | VoskConfig;

            if (engine.type === 'whisper') {
                config = {
                    modelPath: '/models/whisper/tiny.bin',
                    modelSize: 'tiny',
                    language: options.language || 'en',
                    task: 'transcribe'
                } as WhisperConfig;

                offlineEngineRef.current = TranscriptionEngineFactory.createWhisperEngine(config);
            } else if (engine.type === 'vosk') {
                config = {
                    modelPath: '/models/vosk/model.tar.gz',
                    sampleRate: 16000,
                    language: options.language || 'en'
                } as VoskConfig;

                offlineEngineRef.current = TranscriptionEngineFactory.createVoskEngine(config);
            }

            if (offlineEngineRef.current) {
                await offlineEngineRef.current.startTranscription(mediaStream, (text: string) => {
                    setTranscription(prev => prev + text + ' ');
                });
            }

        } catch (error) {
            console.error('Failed to start offline transcription:', error);
            toast({
                title: "Offline Engine Error",
                description: `Failed to start ${engine.name}: ${error.message}`,
                variant: "destructive",
            });
        }
    }, [options.language, toast]);

    // Stop recording
    const stopRecording = useCallback(() => {
        isManualStopRef.current = true;
        setIsRecording(false);
        setInterimText('');
        setAudioLevel(0);

        // Stop Web Speech Recognition
        if (recognitionRef.current) {
            recognitionRef.current.stop();
            recognitionRef.current = null;
        }

        // Stop offline transcription
        if (offlineEngineRef.current) {
            offlineEngineRef.current.stopTranscription();
            offlineEngineRef.current = null;
        }

        // Clean up audio resources
        if (mediaStreamRef.current) {
            mediaStreamRef.current.getTracks().forEach(track => track.stop());
            mediaStreamRef.current = null;
        }

        if (audioContextRef.current) {
            audioContextRef.current.close();
            audioContextRef.current = null;
        }

        analyserRef.current = null;
        setCurrentSource(null);

        toast({
            title: "Recording Stopped",
            description: "Transcription has been saved.",
            variant: "default",
        });
    }, [toast]);

    // Clear transcription
    const clearTranscription = useCallback(() => {
        setTranscription('');
        setInterimText('');
        toast({
            title: "Transcription Cleared",
            description: "Text has been reset.",
            variant: "default",
        });
    }, [toast]);

    // Switch transcription engine
    const switchEngine = useCallback((engine: TranscriptionEngine) => {
        setCurrentEngine(engine);

        if (isRecording) {
            toast({
                title: "Engine Changed",
                description: `Switched to ${engine.name}. Stop and restart recording to apply changes.`,
                variant: "default",
            });
        }
    }, [isRecording, toast]);

    // Get setup instructions
    const getSetupInstructions = useCallback((): string[] => {
        return systemDetectorRef.current.generateSetupInstructions();
    }, []);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (isRecording) {
                stopRecording();
            }
        };
    }, [isRecording, stopRecording]);

    return {
        // State
        isRecording,
        transcription,
        interimText,
        currentSource,
        currentEngine,
        availableEngines,
        systemCapabilities,
        isInitializing,
        audioLevel,

        // Actions
        startRecording,
        stopRecording,
        clearTranscription,
        switchEngine,
        getEnhancedAudioSources,
        getSetupInstructions,

        // Utilities
        isSystemAudioSupported: systemCapabilities?.isSupported || false,
        hasOfflineEngines: availableEngines.some(e => !e.isOnline),
        recommendedSetup: systemCapabilities?.method || 'none'
    };
}; 