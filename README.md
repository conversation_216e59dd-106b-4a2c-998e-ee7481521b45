# 🎯 System Audio Transcription App

A powerful React + TypeScript application that captures and transcribes system audio in real-time. Perfect for transcribing YouTube videos, video calls, media players, and any audio playing through your speakers.

## ✨ Features

### 🎤 **Multiple Audio Sources**
- **Microphone Input**: Standard voice recording
- **System Audio**: Capture speaker output (YouTube, media players)
- **Stereo Mix**: Windows built-in system audio loopback
- **Virtual Audio Cables**: Professional audio routing (VB-Cable, VoiceMeeter)
- **Auto-Detection**: Automatically detects available audio sources

### 🧠 **Multiple Transcription Engines**
- **Web Speech API**: Fast, online transcription (Google)
- **Whisper (Offline)**: OpenAI's Whisper models running locally
- **Vosk (Offline)**: Privacy-focused offline speech recognition
- **Engine Switching**: Change engines on-the-fly

### 🔧 **Advanced Features**
- **Real-time Transcription**: Live text as audio plays
- **Audio Level Monitoring**: Visual feedback of audio input
- **System Capability Detection**: Automatic setup recommendations
- **Infinite Recording**: Continuous transcription until stopped
- **Export Options**: Copy, save, or clear transcriptions
- **Cross-Platform**: Windows, macOS, Linux support

## 🚀 Quick Start

### 1. **Installation**
```bash
git clone <your-repo>
cd system-audio-transcription
npm install
```

### 2. **Basic Setup (Windows)**
```bash
# Enable Stereo Mix (easiest method)
1. Right-click speaker icon → Sound settings
2. Sound Control Panel → Recording tab
3. Right-click → Show Disabled Devices
4. Enable "Stereo Mix" → Set as Default
```

### 3. **Run the App**
```bash
npm run dev
# Open http://localhost:8080
```

### 4. **Start Transcribing**
1. Select "Stereo Mix" as audio source
2. Click "Start Recording"
3. Play YouTube video or any media
4. Watch real-time transcription appear!

## 🎯 Use Cases

### 📺 **YouTube/Media Transcription**
Perfect for:
- Creating captions for videos
- Transcribing educational content
- Converting podcasts to text
- Accessibility improvements

**Setup**: Stereo Mix (Windows) or BlackHole (macOS)

### 💼 **Video Call Transcription**
Ideal for:
- Meeting notes and minutes
- Interview transcriptions
- Online class recordings
- Accessibility in calls

**Setup**: VB-Cable for highest quality

### 🎮 **Gaming/Streaming**
Great for:
- Game dialogue transcription
- Voice chat logging
- Stream accessibility
- Content creation

**Setup**: VB-Cable + OBS integration

## 🔧 Platform Setup

### Windows (Recommended: Stereo Mix)
```bash
# Method 1: Stereo Mix (Built-in)
1. Windows + R → "mmsys.cpl"
2. Recording tab → Show Disabled Devices
3. Enable "Stereo Mix" → Set as Default
4. Test with music playing

# Method 2: VB-Cable (Professional)
1. Download VB-Cable from vb-audio.com
2. Install and restart
3. Set "CABLE Input" as default playback
4. Select "CABLE Output" in app
```

### macOS (BlackHole)
```bash
# Install BlackHole
1. Download from: github.com/ExistentialAudio/BlackHole
2. Install BlackHole 2ch
3. Audio MIDI Setup → Multi-Output Device
4. Include BlackHole + speakers
5. Set as default output
```

### Linux (PulseAudio)
```bash
# Create loopback
pactl load-module module-loopback latency_msec=1

# Find monitor source
pactl list sources short

# Use monitor in app
```

## 🧠 Offline Transcription

### Download Models
```bash
# Download recommended models
npm run download-models all

# Download specific models
npm run download-models whisper tiny
npm run download-models vosk en-small

# See all available models
npm run download-models
```

### Model Comparison
| Engine | Size | Speed | Accuracy | Privacy |
|--------|------|-------|----------|---------|
| Web Speech | 0MB | Fastest | Good | Online |
| Whisper Tiny | 39MB | Fast | Good | Offline |
| Whisper Base | 142MB | Medium | Better | Offline |
| Vosk Small | 40MB | Fast | Good | Offline |
| Vosk Large | 1.8GB | Slow | Best | Offline |

## 🎛️ Advanced Configuration

### Professional Audio Setup
```bash
# For content creators and professionals
1. Install VoiceMeeter (free audio mixer)
2. Route multiple sources (mic + system + apps)
3. Use hardware audio interface
4. Monitor with zero latency
```

### Privacy-Focused Setup
```bash
# For sensitive content
1. Use offline engines only (Whisper/Vosk)
2. Download models locally
3. Disable network access
4. All processing on-device
```

### Multi-Language Support
```bash
# Supported languages
- English (en-US, en-GB)
- Spanish (es-ES)
- French (fr-FR)
- German (de-DE)
- Italian (it-IT)
- Portuguese (pt-BR)
- Russian (ru-RU)
- Japanese (ja-JP)
- Korean (ko-KR)
- Chinese (zh-CN)
- Arabic (ar)
- Hindi (hi)
```

## 🔍 Troubleshooting

### Common Issues

#### "No System Audio Detected"
```bash
✅ Solutions:
- Enable Stereo Mix in Windows
- Check if audio is actually playing
- Restart browser after enabling Stereo Mix
- Grant microphone permissions
```

#### "Poor Transcription Quality"
```bash
✅ Solutions:
- Adjust system volume to 50-80%
- Use VB-Cable for better routing
- Try different transcription engines
- Check language settings
- Reduce background noise
```

#### "Audio Capture Failed"
```bash
✅ Solutions:
- Grant browser microphone permissions
- Update audio drivers
- Close other apps using audio
- Try different audio source
- Restart browser/computer
```

#### "High Latency"
```bash
✅ Solutions:
- Use VB-Cable instead of Stereo Mix
- Reduce buffer size in settings
- Close unnecessary applications
- Use wired headphones
- Enable hardware acceleration
```

## 🏗️ Architecture

### Frontend (React + TypeScript)
```
src/
├── components/
│   ├── SystemAudioTranscriptionApp.tsx  # Main app component
│   ├── AudioSourceSelector.tsx          # Audio source selection
│   └── TranscriptionDisplay.tsx         # Text display
├── hooks/
│   ├── useSystemAudioTranscription.ts   # Main transcription logic
│   └── useAudioDevices.ts               # Audio device management
├── utils/
│   ├── systemAudioDetector.ts           # System capability detection
│   └── offlineTranscription.ts          # Offline engine management
├── workers/
│   └── transcriptionWorker.ts           # Web Worker for processing
└── types/
    └── audio.d.ts                       # TypeScript definitions
```

### Key Technologies
- **React 18**: Modern UI framework
- **TypeScript**: Type safety and better DX
- **Web Audio API**: Audio processing and analysis
- **Web Speech API**: Browser-native speech recognition
- **Web Workers**: Offline processing without blocking UI
- **Vite**: Fast development and building

## 🚀 Deployment

### Development
```bash
npm run dev          # Start development server
npm run lint         # Check code quality
npm run download-models  # Download offline models
```

### Production
```bash
npm run build        # Build for production
npm run preview      # Preview production build
```

### Docker (Optional)
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
EXPOSE 8080
CMD ["npm", "run", "preview"]
```

## 📊 Performance

### System Requirements
- **RAM**: 4GB minimum, 8GB recommended
- **CPU**: Modern dual-core processor
- **Storage**: 500MB for app + models
- **Browser**: Chrome 88+, Edge 88+, Firefox 85+

### Performance Tips
- Use Chrome/Edge for best compatibility
- Close unnecessary browser tabs
- Use wired headphones to prevent feedback
- Adjust system volume to 50-80%
- Enable hardware acceleration

## 🔒 Privacy & Security

### Data Handling
- **Web Speech API**: Audio sent to Google servers
- **Offline Engines**: All processing on your device
- **No Storage**: Transcriptions not saved automatically
- **Local Models**: Downloaded models stay on device

### Recommendations
- Use offline engines for sensitive content
- Review browser permissions regularly
- Be aware of local recording laws
- Get consent before recording calls

## 🤝 Contributing

### Development Setup
```bash
git clone <repo>
cd system-audio-transcription
npm install
npm run dev
```

### Code Style
- TypeScript strict mode
- ESLint + Prettier
- Conventional commits
- Component-based architecture

### Adding New Features
1. Fork the repository
2. Create feature branch
3. Add tests if applicable
4. Submit pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

### Getting Help
1. Check the [Setup Guide](SYSTEM_AUDIO_SETUP.md)
2. Review troubleshooting section
3. Check browser console for errors
4. Open GitHub issue with details

### Known Limitations
- System audio requires manual setup
- Firefox has limited system audio support
- Safari doesn't support all features
- Some corporate networks block audio capture

## 🎉 Success Stories

> "Finally able to transcribe my online meetings automatically!" - Developer

> "Perfect for creating captions for my YouTube videos." - Content Creator

> "The offline mode is great for privacy-sensitive work." - Enterprise User

---

**Ready to start transcribing system audio?** 

1. Follow the [Quick Start](#-quick-start) guide
2. Check the [Setup Guide](SYSTEM_AUDIO_SETUP.md) for detailed instructions
3. Start with Stereo Mix (easiest) or VB-Cable (professional)
4. Begin transcribing YouTube, calls, and any system audio!

🎤→📝 **Happy Transcribing!**
