import { useState, useEffect, useCallback } from 'react';
import { AudioDevice, AudioInputSource } from '@/types/audio';
import { SystemAudioDetector } from '@/utils/systemAudioDetector';

export const useAudioDevices = () => {
    const [audioDevices, setAudioDevices] = useState<AudioDevice[]>([]);
    const [availableSources, setAvailableSources] = useState<AudioInputSource[]>([]);
    const [selectedSource, setSelectedSource] = useState<AudioInputSource | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const getAudioDevices = useCallback(async () => {
        try {
            setIsLoading(true);
            setError(null);

            // Use enhanced system audio detector
            const systemDetector = SystemAudioDetector.getInstance();
            const enhancedSources = await systemDetector.getEnhancedAudioSources();

            // Also get basic device info for compatibility
            const devices = await navigator.mediaDevices.enumerateDevices();
            const audioInputDevices = devices
                .filter(device => device.kind === 'audioinput')
                .map(device => ({
                    deviceId: device.deviceId,
                    label: device.label || `Microphone ${device.deviceId.slice(0, 8)}`,
                    kind: device.kind,
                    groupId: device.groupId
                }));

            setAudioDevices(audioInputDevices);
            setAvailableSources(enhancedSources);

            // Set default source to the first high-quality source
            if (enhancedSources.length > 0 && !selectedSource) {
                // Prefer system audio sources if available
                const systemAudioSource = enhancedSources.find(s =>
                    s.capabilities?.supportsSystemAudio && s.capabilities.quality === 'high'
                );
                setSelectedSource(systemAudioSource || enhancedSources[0]);
            }

        } catch (err) {
            console.error('Error getting audio devices:', err);
            setError('Failed to access audio devices. Please check permissions.');
        } finally {
            setIsLoading(false);
        }
    }, [selectedSource]);

    const selectSource = useCallback((source: AudioInputSource) => {
        setSelectedSource(source);
    }, []);

    const refreshDevices = useCallback(() => {
        getAudioDevices();
    }, [getAudioDevices]);

    useEffect(() => {
        getAudioDevices();

        // Listen for device changes
        const handleDeviceChange = () => {
            getAudioDevices();
        };

        navigator.mediaDevices.addEventListener('devicechange', handleDeviceChange);

        return () => {
            navigator.mediaDevices.removeEventListener('devicechange', handleDeviceChange);
        };
    }, [getAudioDevices]);

    return {
        audioDevices,
        availableSources,
        selectedSource,
        isLoading,
        error,
        selectSource,
        refreshDevices
    };
}; 