// Web Worker for handling transcription processing
import { WhisperConfig, VoskConfig } from '../utils/offlineTranscription';

interface WorkerMessage {
    type: 'initialize' | 'audioData' | 'stop';
    engineType?: 'whisper' | 'vosk';
    config?: WhisperConfig | VoskConfig;
    data?: Int16Array;
}

class TranscriptionWorker {
    private engine: any = null;
    private engineType: 'whisper' | 'vosk' | null = null;
    private isInitialized = false;
    private audioBuffer: Int16Array[] = [];
    private bufferSize = 0;
    private maxBufferSize = 16000 * 5; // 5 seconds at 16kHz

    async initialize(engineType: 'whisper' | 'vosk', config: WhisperConfig | VoskConfig) {
        this.engineType = engineType;

        try {
            if (engineType === 'whisper') {
                await this.initializeWhisper(config as WhisperConfig);
            } else if (engineType === 'vosk') {
                await this.initializeVosk(config as VoskConfig);
            }

            this.isInitialized = true;
            self.postMessage({ type: 'initialized' });
        } catch (error) {
            self.postMessage({
                type: 'error',
                data: { error: `Failed to initialize ${engineType}: ${error.message}` }
            });
        }
    }

    private async initializeWhisper(config: WhisperConfig) {
        try {
            // Check if Whisper models are available
            const modelResponse = await fetch(config.modelPath, { method: 'HEAD' });
            if (!modelResponse.ok) {
                throw new Error('Whisper model not found');
            }

            // For now, we'll use the mock implementation
            // In production, you would load the actual Whisper.cpp WASM module here
            console.warn('Using mock Whisper implementation - install whisper.cpp for real functionality');
            this.engine = new MockWhisperEngine(config);
        } catch (error) {
            // Fallback to mock implementation for demo
            console.warn('Whisper not available, using mock implementation');
            this.engine = new MockWhisperEngine(config);
        }
    }

    private async initializeVosk(config: VoskConfig) {
        try {
            // Check if Vosk models are available
            const modelResponse = await fetch(config.modelPath, { method: 'HEAD' });
            if (!modelResponse.ok) {
                throw new Error('Vosk model not found');
            }

            // For now, we'll use the mock implementation
            // In production, you would load the actual Vosk WASM module here
            console.warn('Using mock Vosk implementation - install vosk-browser for real functionality');
            this.engine = new MockVoskEngine(config);
        } catch (error) {
            // Fallback to mock implementation for demo
            console.warn('Vosk not available, using mock implementation');
            this.engine = new MockVoskEngine(config);
        }
    }

    processAudioData(audioData: Int16Array) {
        if (!this.isInitialized || !this.engine) return;

        // Add to buffer
        this.audioBuffer.push(audioData);
        this.bufferSize += audioData.length;

        // Process when we have enough data (1 second worth)
        if (this.bufferSize >= 16000) {
            this.processBuffer();
        }
    }

    private async processBuffer() {
        if (this.audioBuffer.length === 0) return;

        try {
            // Combine buffer chunks
            const combinedBuffer = new Int16Array(this.bufferSize);
            let offset = 0;

            for (const chunk of this.audioBuffer) {
                combinedBuffer.set(chunk, offset);
                offset += chunk.length;
            }

            // Process with the appropriate engine
            let result: string = '';

            if (this.engineType === 'whisper') {
                result = await this.engine.transcribe(combinedBuffer);
            } else if (this.engineType === 'vosk') {
                result = await this.engine.recognize(combinedBuffer);
            }

            if (result && result.trim()) {
                self.postMessage({
                    type: 'transcription',
                    data: { text: result.trim() }
                });
            }

            // Clear processed buffer but keep some overlap
            const overlapSize = Math.floor(this.bufferSize * 0.1); // 10% overlap
            if (this.bufferSize > overlapSize) {
                const overlapData = combinedBuffer.slice(-overlapSize);
                this.audioBuffer = [overlapData];
                this.bufferSize = overlapSize;
            } else {
                this.audioBuffer = [];
                this.bufferSize = 0;
            }

        } catch (error) {
            self.postMessage({
                type: 'error',
                data: { error: `Transcription error: ${error.message}` }
            });
        }
    }

    stop() {
        this.audioBuffer = [];
        this.bufferSize = 0;

        if (this.engine && this.engine.cleanup) {
            this.engine.cleanup();
        }
    }
}

// Mock implementations for development/fallback
class MockWhisperEngine {
    private config: WhisperConfig;
    private lastTranscription = '';

    constructor(config: WhisperConfig) {
        this.config = config;
    }

    async transcribe(audioData: Int16Array): Promise<string> {
        // Simulate processing delay
        await new Promise(resolve => setTimeout(resolve, 100));
        console.log('audioData', audioData);
        // Mock transcription based on audio energy
        const energy = this.calculateAudioEnergy(audioData);

        if (energy > 0.01) {
            const mockPhrases = [
                "This is a mock transcription from Whisper.",
                "System audio is being processed.",
                "Real-time transcription is working.",
                "Audio energy detected in the stream.",
                "Whisper engine is processing audio data."
            ];

            const randomPhrase = mockPhrases[Math.floor(Math.random() * mockPhrases.length)];
            this.lastTranscription = randomPhrase;
            return randomPhrase;
        }

        return '';
    }

    private calculateAudioEnergy(audioData: Int16Array): number {
        let sum = 0;
        for (let i = 0; i < audioData.length; i++) {
            sum += Math.abs(audioData[i]);
        }
        return sum / (audioData.length * 32768); // Normalize to 0-1
    }
}

class MockVoskEngine {
    private config: VoskConfig;

    constructor(config: VoskConfig) {
        this.config = config;
    }

    async recognize(audioData: Int16Array): Promise<string> {
        // Simulate processing delay
        await new Promise(resolve => setTimeout(resolve, 150));

        // Mock transcription
        const energy = this.calculateAudioEnergy(audioData);

        if (energy > 0.01) {
            const mockPhrases = [
                "Vosk is processing system audio.",
                "Real-time speech recognition active.",
                "Audio stream being transcribed.",
                "Background audio detected.",
                "Continuous transcription running."
            ];

            return mockPhrases[Math.floor(Math.random() * mockPhrases.length)];
        }

        return '';
    }

    private calculateAudioEnergy(audioData: Int16Array): number {
        let sum = 0;
        for (let i = 0; i < audioData.length; i++) {
            sum += Math.abs(audioData[i]);
        }
        return sum / (audioData.length * 32768);
    }
}

// Worker message handler
const worker = new TranscriptionWorker();

self.onmessage = async (event: MessageEvent<WorkerMessage>) => {
    const { type, engineType, config, data } = event.data;

    switch (type) {
        case 'initialize':
            if (engineType && config) {
                await worker.initialize(engineType, config);
            }
            break;

        case 'audioData':
            if (data) {
                worker.processAudioData(data);
            }
            break;

        case 'stop':
            worker.stop();
            break;
    }
};

export { }; 