{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "outDir": "../dist-electron", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "noEmit": false, "declaration": false, "sourceMap": false}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist-electron"]}