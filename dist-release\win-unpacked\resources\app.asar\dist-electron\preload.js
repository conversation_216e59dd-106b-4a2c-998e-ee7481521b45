"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// Custom APIs for renderer
const electronAPI = {
    // File operations
    showSaveDialog: () => electron_1.ipcRenderer.invoke('show-save-dialog'),
    showOpenDialog: () => electron_1.ipcRenderer.invoke('show-open-dialog'),
    // App info
    getAppVersion: () => electron_1.ipcRenderer.invoke('get-app-version'),
    // Menu events
    onMenuNew: (callback) => {
        electron_1.ipcRenderer.on('menu-new', callback);
        return () => electron_1.ipcRenderer.removeListener('menu-new', callback);
    },
    onMenuSave: (callback) => {
        electron_1.ipcRenderer.on('menu-save', callback);
        return () => electron_1.ipcRenderer.removeListener('menu-save', callback);
    },
    // Platform info
    platform: process.platform,
    // Check if running in Electron
    isElectron: true
};
// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
    try {
        electron_1.contextBridge.exposeInMainWorld('electronAPI', electronAPI);
    }
    catch (error) {
        console.error(error);
    }
}
else {
    // @ts-ignore (define in dts)
    window.electronAPI = electronAPI;
}
