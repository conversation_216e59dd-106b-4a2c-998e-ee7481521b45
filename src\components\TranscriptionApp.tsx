import React, { useState } from 'react';
import { Mi<PERSON>, Mic<PERSON><PERSON>, Square, Trash2, <PERSON><PERSON><PERSON>, Refresh<PERSON>w, Cpu, Wifi } from 'lucide-react';
import ControlButton from './ControlButton';
import TranscriptionDisplay from './TranscriptionDisplay';
import AudioSourceSelector from './AudioSourceSelector';
import { useToast } from '@/hooks/use-toast';
import { useSystemAudioTranscription } from '@/hooks/useSystemAudioTranscription';
import { useAudioDevices } from '@/hooks/useAudioDevices';
import { AudioInputSource, TranscriptionEngine } from '@/types/audio';

const TranscriptionApp = () => {
  const [showSettings, setShowSettings] = useState(false);
  const [showEngineSettings, setShowEngineSettings] = useState(false);
  const { selectedSource, selectSource } = useAudioDevices();
  const {
    isRecording,
    transcription,
    interimText,
    currentSource,
    currentEngine,
    availableEngines,
    systemCapabilities,
    isInitializing,
    audioLevel,
    startRecording,
    stopRecording,
    clearTranscription,
    switchEngine,
    getEnhancedAudioSources,
    getSetupInstructions,
    isSystemAudioSupported,
    hasOfflineEngines,
    recommendedSetup
  } = useSystemAudioTranscription({
    preferredEngine: 'web-speech',
    enableOfflineMode: true,
    language: 'en-US'
  });
  const { toast } = useToast();

  const handleStartRecording = () => {
    if (!selectedSource) {
      toast({
        title: "No Audio Source Selected",
        description: "Please select an audio input source before starting recording.",
        variant: "destructive",
      });
      return;
    }
    startRecording(selectedSource, currentEngine || undefined);
  };

  const handleSourceSelect = (source: AudioInputSource) => {
    selectSource(source);
    if (isRecording) {
      toast({
        title: "Audio Source Changed",
        description: "Stop and restart recording to use the new audio source.",
        variant: "default",
      });
    }
  };

  if (isInitializing) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-xl p-8 text-center max-w-md">
          <div className="text-blue-500 mb-4">
            <RefreshCw size={48} className="mx-auto animate-spin" />
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Initializing</h2>
          <p className="text-gray-600">
            Detecting system audio capabilities and transcription engines...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            Enhanced Live Transcription
          </h1>
          <p className="text-gray-600 text-lg">
            Real-time voice-to-text transcription with multiple audio input sources
          </p>
        </div>

        {/* Audio Source Settings */}
        <div className="bg-white rounded-2xl shadow-xl p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800">Audio Settings</h2>
            <ControlButton
              onClick={() => setShowSettings(!showSettings)}
              variant="secondary"
              icon={<Settings size={16} />}
            >
              {showSettings ? 'Hide' : 'Show'} Settings
            </ControlButton>
          </div>

          {showSettings && (
            <div className="border-t pt-4">
              <AudioSourceSelector
                onSourceSelect={handleSourceSelect}
                disabled={isRecording}
                currentSource={selectedSource}
              />
            </div>
          )}

          {!showSettings && selectedSource && (
            <div className="text-sm text-gray-600">
              <span className="font-medium">Current source:</span> {selectedSource.name}
              {selectedSource.type === 'system' && (
                <span className="ml-2 text-blue-600">(System Audio)</span>
              )}
            </div>
          )}
        </div>

        {/* Control Panel */}
        <div className="bg-white rounded-2xl shadow-xl p-6 mb-6">
          <div className="flex items-center justify-center gap-4">
            <ControlButton
              onClick={handleStartRecording}
              disabled={isRecording || !selectedSource}
              variant="primary"
              icon={<Mic size={20} />}
            >
              Start Recording
            </ControlButton>

            <ControlButton
              onClick={() => {
                console.log('Stop button clicked');
                stopRecording();
              }}
              disabled={!isRecording}
              variant="secondary"
              icon={<Square size={20} />}
            >
              Stop Recording
            </ControlButton>

            <ControlButton
              onClick={clearTranscription}
              disabled={isRecording}
              variant="danger"
              icon={<Trash2 size={20} />}
            >
              Clear
            </ControlButton>
          </div>

          {/* Recording Status */}
          {isRecording && (
            <div className="mt-4 flex items-center justify-center gap-2 text-green-600">
              <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              <span className="font-medium">
                Recording from {currentSource?.name || 'Unknown source'}...
              </span>
              {consecutiveErrors > 0 && consecutiveErrors < 3 && (
                <span className="text-xs text-orange-500 ml-2">
                  (Auto-recovering from connection issues - {consecutiveErrors}/5)
                </span>
              )}
              {consecutiveErrors >= 3 && (
                <span className="text-xs text-red-500 ml-2">
                  (Multiple connection issues detected)
                </span>
              )}
            </div>
          )}

          {!selectedSource && !isRecording && (
            <div className="mt-4 text-center text-amber-600 bg-amber-50 rounded-lg p-3">
              <p className="text-sm font-medium">⚠️ No audio source selected</p>
              <p className="text-xs mt-1">Please select an audio input source to begin recording</p>
            </div>
          )}

          {consecutiveErrors >= 3 && !isRecording && (
            <div className="mt-4 text-center text-red-600 bg-red-50 rounded-lg p-3">
              <p className="text-sm font-medium">🚨 Audio Input Issues Detected</p>
              <p className="text-xs mt-1">Try these troubleshooting steps:</p>
              <ul className="text-xs mt-2 text-left list-disc list-inside space-y-1">
                <li>Check that your microphone is connected and working</li>
                <li>Refresh the audio devices list</li>
                <li>Try selecting a different audio source</li>
                <li>For system audio: Enable "Stereo Mix" or use virtual audio cable</li>
                <li>Check browser permissions for microphone access</li>
              </ul>
              <div className="mt-3">
                <ControlButton
                  onClick={resetErrors}
                  variant="secondary"
                  icon={<RefreshCw size={16} />}
                >
                  Reset & Try Again
                </ControlButton>
              </div>
            </div>
          )}
        </div>

        {/* Transcription Display */}
        <TranscriptionDisplay
          transcription={transcription}
          interimText={interimText}
          isRecording={isRecording}
        />

        {/* Help Section */}
        <div className="mt-6 bg-white rounded-2xl shadow-xl p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">📋 How to capture different audio sources:</h3>
          <div className="grid md:grid-cols-2 gap-4 text-sm text-gray-600">
            <div className="space-y-2">
              <h4 className="font-medium text-gray-800">🎤 Microphone Input:</h4>
              <ul className="list-disc list-inside space-y-1">
                <li>Select any connected microphone</li>
                <li>Works with built-in or external mics</li>
                <li>Best for voice recording</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-gray-800">🔊 System Audio (YouTube, etc.):</h4>
              <ul className="list-disc list-inside space-y-1">
                <li><strong>Enable "Stereo Mix"</strong> in Windows Sound settings</li>
                <li><strong>Select "Stereo Mix" as Microphone input</strong> (not System Audio)</li>
                <li>Alternative: Use virtual audio cable software (VB-Cable, VoiceMeeter)</li>
                <li>Direct system audio capture is not supported by browsers</li>
              </ul>
            </div>
          </div>
          <div className="mt-4 p-3 bg-orange-50 rounded-lg border border-orange-200">
            <p className="text-xs text-orange-700">
              <strong>Important:</strong> Direct system audio capture is not supported by web browsers.
              To transcribe YouTube, music, or other system audio, you must enable "Stereo Mix" in Windows Sound settings
              and select it as a Microphone input, or use virtual audio cable software.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TranscriptionApp;
