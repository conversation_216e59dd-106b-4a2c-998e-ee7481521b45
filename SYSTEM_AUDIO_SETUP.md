# 🎯 System Audio Transcription Setup Guide

This guide will help you set up your system to capture and transcribe audio from YouTube, video calls, media players, and any other system audio.

## 🚀 Quick Start

### Option 1: Stereo Mix (Windows - Recommended)

**Best for:** YouTube, media players, general system audio

1. **Enable Stereo Mix:**
   - Right-click the speaker icon in your system tray
   - Select "Open Sound settings"
   - Click "Sound Control Panel" (or "More sound settings")
   - Go to the "Recording" tab
   - Right-click in empty space → "Show Disabled Devices"
   - Find "Stereo Mix" → Right-click → "Enable"
   - Right-click "Stereo Mix" → "Set as Default Device"

2. **Use in the App:**
   - Open the transcription app
   - In Audio Source settings, select "Stereo Mix"
   - Click "Start Recording"
   - Play your YouTube video or media
   - Watch real-time transcription appear!

### Option 2: VB-Audio Cable (Advanced)

**Best for:** Video calls, professional streaming, highest quality

1. **Install VB-Cable:**
   - Download from: https://vb-audio.com/Cable/
   - Install and restart your computer

2. **Configure Audio Routing:**
   - Set "CABLE Input" as your default playback device
   - In the app, select "CABLE Output" as audio source

3. **For Video Calls:**
   - Set your microphone as input in Zoom/Meet
   - Set "CABLE Input" as speaker output
   - The app will capture both your voice and others!

## 🎮 Platform-Specific Instructions

### Windows 10/11

#### Method 1: Stereo Mix
```
1. Windows Key + R → type "mmsys.cpl" → Enter
2. Recording tab → Right-click → Show Disabled Devices
3. Enable "Stereo Mix" → Set as Default
4. Test: Play music, check if Stereo Mix shows activity
```

#### Method 2: VB-Audio Cable
```
1. Download VB-Cable from official site
2. Run as Administrator → Install → Restart
3. Sound Settings → Set "CABLE Input" as default output
4. In app: Select "CABLE Output" as input source
```

### macOS

#### BlackHole (Free)
```
1. Download BlackHole: https://github.com/ExistentialAudio/BlackHole
2. Install BlackHole 2ch
3. Audio MIDI Setup → Create Multi-Output Device
4. Include BlackHole + your speakers
5. Set Multi-Output as default output
6. In app: Select BlackHole as input
```

#### SoundFlower (Alternative)
```
1. Download from: https://github.com/mattingalls/Soundflower
2. Install and restart
3. Set Soundflower as output device
4. Use Soundflower as input in the app
```

### Linux

#### PulseAudio Loopback
```bash
# Create loopback module
pactl load-module module-loopback latency_msec=1

# List sources to find monitor
pactl list sources short

# Use monitor source in the app
```

## 🎯 Use Case Specific Setup

### YouTube/Media Transcription
1. **Setup:** Stereo Mix (Windows) or BlackHole (macOS)
2. **Process:**
   - Enable system audio capture
   - Open YouTube video
   - Start recording in the app
   - Get real-time captions!

### Video Call Transcription (Zoom, Meet, Teams)
1. **Setup:** VB-Cable (recommended for quality)
2. **Configuration:**
   - Install VB-Cable
   - In video call app: Set speaker to "CABLE Input"
   - In transcription app: Select "CABLE Output"
   - Set your microphone normally in video call
3. **Result:** Transcribes all participants + you!

### Gaming/Streaming
1. **Setup:** VB-Cable + OBS integration
2. **Benefits:**
   - Capture game audio + voice chat
   - Real-time transcription for accessibility
   - Stream overlay integration possible

### Podcast/Audio Content
1. **Setup:** Any method works
2. **Tips:**
   - Use highest quality settings
   - Enable noise suppression in app
   - Consider offline engines for privacy

## 🔧 Troubleshooting

### "No System Audio Detected"
- **Check:** Is Stereo Mix enabled and set as default?
- **Verify:** Play music - does Stereo Mix show green bars?
- **Try:** Restart the app after enabling Stereo Mix

### "Audio Capture Failed"
- **Permissions:** Grant microphone access to browser
- **Drivers:** Update audio drivers
- **Conflicts:** Close other apps using audio

### "Poor Transcription Quality"
- **Volume:** Ensure system volume is 50-80%
- **Quality:** Use VB-Cable for better audio routing
- **Engine:** Try different transcription engines
- **Language:** Verify correct language is selected

### "Latency Issues"
- **Buffer:** Reduce buffer size in advanced settings
- **Method:** VB-Cable has lower latency than Stereo Mix
- **Hardware:** Use dedicated audio interface if available

## 🎛️ Advanced Configuration

### Multiple Audio Sources
```
1. Install VoiceMeeter (free mixing software)
2. Route multiple inputs (mic + system + apps)
3. Output mixed audio to virtual cable
4. Transcribe the mixed stream
```

### Professional Setup
```
1. Audio Interface (Focusrite, PreSonus)
2. VoiceMeeter Pro for advanced routing
3. Multiple virtual cables for different sources
4. Hardware monitoring for zero-latency
```

### Privacy-Focused Setup
```
1. Use offline transcription engines (Whisper/Vosk)
2. Local model files (no internet required)
3. All processing happens on your device
4. No data sent to external services
```

## 📊 Quality Comparison

| Method | Quality | Latency | Complexity | Best For |
|--------|---------|---------|------------|----------|
| Stereo Mix | Good | Medium | Low | YouTube, Media |
| VB-Cable | Excellent | Low | Medium | Video Calls, Pro |
| BlackHole | Excellent | Low | Medium | macOS Users |
| Hardware Interface | Best | Lowest | High | Professional |

## 🚨 Important Notes

### Browser Compatibility
- **Chrome/Edge:** Full support for all features
- **Firefox:** Limited system audio support
- **Safari:** Basic functionality only

### Performance Tips
- **Close unnecessary apps** during transcription
- **Use wired headphones** to prevent feedback
- **Adjust system volume** to 50-80% for best results
- **Enable hardware acceleration** in browser settings

### Legal Considerations
- **Consent:** Ensure you have permission to record calls
- **Privacy:** Be aware of local recording laws
- **Data:** Consider using offline engines for sensitive content

## 🎯 Quick Test

1. **Setup your preferred method** (Stereo Mix recommended for beginners)
2. **Open the transcription app**
3. **Select your system audio source**
4. **Play this test video:** https://www.youtube.com/watch?v=dQw4w9WgXcQ
5. **Click "Start Recording"**
6. **Verify transcription appears** in real-time

## 🆘 Need Help?

If you're still having issues:

1. **Check the app's System Status** panel for detected capabilities
2. **Review the setup instructions** for your specific method
3. **Try the browser's developer console** for error messages
4. **Test with different audio sources** to isolate the issue

## 🎉 Success!

Once set up correctly, you'll be able to:
- ✅ Transcribe YouTube videos in real-time
- ✅ Get live captions for video calls
- ✅ Convert any system audio to text
- ✅ Use both online and offline transcription
- ✅ Capture high-quality audio with minimal latency

Happy transcribing! 🎤→📝 