import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Mic, Monitor, RefreshCw, AlertCircle } from 'lucide-react';
import { AudioInputSource } from '@/types/audio';
import { useAudioDevices } from '@/hooks/useAudioDevices';

interface AudioSourceSelectorProps {
    onSourceSelect: (source: AudioInputSource) => void;
    disabled?: boolean;
    currentSource?: AudioInputSource | null;
}

const AudioSourceSelector = ({ onSourceSelect, disabled = false, currentSource }: AudioSourceSelectorProps) => {
    const { availableSources, isLoading, error, refreshDevices } = useAudioDevices();

    const getSourceIcon = (type: string) => {
        switch (type) {
            case 'microphone':
                return <Mic size={16} />;
            case 'system':
                return <Monitor size={16} />;
            default:
                return <Mic size={16} />;
        }
    };

    const getSourceDescription = (source: AudioInputSource) => {
        switch (source.type) {
            case 'microphone':
                return 'Capture from microphone';
            case 'system':
                return 'System audio (requires setup - see instructions below)';
            default:
                return 'Audio input source';
        }
    };

    if (error) {
        return (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center gap-2 text-red-700 mb-2">
                    <AlertCircle size={16} />
                    <span className="font-medium">Audio Device Error</span>
                </div>
                <p className="text-red-600 text-sm mb-3">{error}</p>
                <Button
                    onClick={refreshDevices}
                    variant="outline"
                    size="sm"
                    className="text-red-700 border-red-300 hover:bg-red-50"
                >
                    <RefreshCw size={14} className="mr-1" />
                    Retry
                </Button>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">
                    Audio Input Source
                </label>
                <Button
                    onClick={refreshDevices}
                    variant="ghost"
                    size="sm"
                    disabled={isLoading || disabled}
                    className="text-gray-500 hover:text-gray-700"
                >
                    <RefreshCw size={14} className={`mr-1 ${isLoading ? 'animate-spin' : ''}`} />
                    Refresh
                </Button>
            </div>

            <Select
                value={currentSource?.id || ''}
                onValueChange={(value) => {
                    const source = availableSources.find(s => s.id === value);
                    console.log('source', source);
                    if (source) {
                        onSourceSelect(source);
                    }
                }}
                disabled={disabled || isLoading}
            >
                <SelectTrigger className="w-full">
                    <SelectValue placeholder={isLoading ? "Loading devices..." : "Select audio source"} />
                </SelectTrigger>
                <SelectContent>
                    {availableSources.map((source) => (
                        <SelectItem key={source.id} value={source.id}>
                            <div className="flex items-center gap-2">
                                {getSourceIcon(source.type)}
                                <div className="flex flex-col">
                                    <span className="font-medium">{source.name}</span>
                                    <span className="text-xs text-gray-500">
                                        {getSourceDescription(source)}
                                    </span>
                                </div>
                            </div>
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>

            {currentSource && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div className="flex items-center gap-2 text-blue-700">
                        {getSourceIcon(currentSource.type)}
                        <span className="font-medium text-sm">Selected: {currentSource.name}</span>
                    </div>
                    <p className="text-blue-600 text-xs mt-1">
                        {getSourceDescription(currentSource)}
                    </p>
                    {currentSource.type === 'system' && (
                        <div className="mt-2 text-xs text-orange-600 bg-orange-50 p-2 rounded">
                            <p className="font-medium">⚠️ System Audio Setup Required:</p>
                            <ul className="list-disc list-inside mt-1 space-y-1">
                                <li><strong>Enable "Stereo Mix"</strong> in Windows Sound settings, then select it as a Microphone input</li>
                                <li><strong>Or use virtual audio cable</strong> software (VB-Cable, VoiceMeeter)</li>
                                <li>Direct system audio capture is not supported by browsers</li>
                            </ul>
                            <p className="mt-1 font-medium text-orange-700">
                                This option will not work directly - please use Microphone input with Stereo Mix instead.
                            </p>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
};

export default AudioSourceSelector;