#!/usr/bin/env node

/**
 * Script to download offline transcription models
 * Run with: npm run download-models
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

const MODELS_DIR = path.join(__dirname, '..', 'public', 'models');

// Model URLs (these would be actual model URLs in production)
const MODELS = {
    whisper: {
        tiny: {
            url: 'https://huggingface.co/ggerganov/whisper.cpp/resolve/main/ggml-tiny.bin',
            size: '39 MB',
            description: 'Fastest, lowest accuracy'
        },
        base: {
            url: 'https://huggingface.co/ggerganov/whisper.cpp/resolve/main/ggml-base.bin',
            size: '142 MB',
            description: 'Good balance of speed and accuracy'
        },
        small: {
            url: 'https://huggingface.co/ggerganov/whisper.cpp/resolve/main/ggml-small.bin',
            size: '466 MB',
            description: 'Better accuracy, slower'
        }
    },
    vosk: {
        'en-small': {
            url: 'https://alphacephei.com/vosk/models/vosk-model-small-en-us-0.15.zip',
            size: '40 MB',
            description: 'English small model'
        },
        'en-large': {
            url: 'https://alphacephei.com/vosk/models/vosk-model-en-us-0.22.zip',
            size: '1.8 GB',
            description: 'English large model (best quality)'
        }
    }
};

function ensureDir(dir) {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
}

function downloadFile(url, dest) {
    return new Promise((resolve, reject) => {
        const file = fs.createWriteStream(dest);

        https.get(url, (response) => {
            if (response.statusCode === 302 || response.statusCode === 301) {
                // Handle redirects
                return downloadFile(response.headers.location, dest);
            }

            if (response.statusCode !== 200) {
                reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
                return;
            }

            const totalSize = parseInt(response.headers['content-length'], 10);
            let downloadedSize = 0;

            response.on('data', (chunk) => {
                downloadedSize += chunk.length;
                const progress = ((downloadedSize / totalSize) * 100).toFixed(1);
                process.stdout.write(`\r  Progress: ${progress}% (${Math.round(downloadedSize / 1024 / 1024)}MB)`);
            });

            response.pipe(file);

            file.on('finish', () => {
                file.close();
                console.log('\n  ✅ Download complete!');
                resolve();
            });

            file.on('error', (err) => {
                fs.unlink(dest, () => { }); // Delete partial file
                reject(err);
            });
        }).on('error', reject);
    });
}

async function downloadModel(engine, modelName, modelInfo) {
    const engineDir = path.join(MODELS_DIR, engine);
    ensureDir(engineDir);

    const filename = path.basename(modelInfo.url);
    const dest = path.join(engineDir, filename);

    if (fs.existsSync(dest)) {
        console.log(`  ⏭️  ${modelName} already exists, skipping...`);
        return;
    }

    console.log(`\n📥 Downloading ${engine}/${modelName}:`);
    console.log(`  📄 ${modelInfo.description}`);
    console.log(`  📦 Size: ${modelInfo.size}`);
    console.log(`  🔗 URL: ${modelInfo.url}`);

    try {
        await downloadFile(modelInfo.url, dest);
        console.log(`  💾 Saved to: ${dest}`);
    } catch (error) {
        console.error(`  ❌ Failed to download ${modelName}:`, error.message);
    }
}

async function main() {
    console.log('🎯 Offline Transcription Model Downloader\n');

    const args = process.argv.slice(2);

    if (args.length === 0) {
        console.log('Available models:');
        console.log('\n🎤 Whisper Models:');
        Object.entries(MODELS.whisper).forEach(([name, info]) => {
            console.log(`  • ${name}: ${info.description} (${info.size})`);
        });

        console.log('\n🗣️  Vosk Models:');
        Object.entries(MODELS.vosk).forEach(([name, info]) => {
            console.log(`  • ${name}: ${info.description} (${info.size})`);
        });

        console.log('\nUsage:');
        console.log('  npm run download-models whisper tiny    # Download Whisper tiny model');
        console.log('  npm run download-models vosk en-small   # Download Vosk English small');
        console.log('  npm run download-models all             # Download recommended models');
        console.log('  npm run download-models whisper all     # Download all Whisper models');
        return;
    }

    ensureDir(MODELS_DIR);

    if (args[0] === 'all') {
        console.log('📦 Downloading recommended models...\n');
        await downloadModel('whisper', 'tiny', MODELS.whisper.tiny);
        await downloadModel('vosk', 'en-small', MODELS.vosk['en-small']);
    } else if (args[0] === 'whisper') {
        if (args[1] === 'all') {
            for (const [name, info] of Object.entries(MODELS.whisper)) {
                await downloadModel('whisper', name, info);
            }
        } else if (args[1] && MODELS.whisper[args[1]]) {
            await downloadModel('whisper', args[1], MODELS.whisper[args[1]]);
        } else {
            console.error('❌ Invalid Whisper model. Available: tiny, base, small');
        }
    } else if (args[0] === 'vosk') {
        if (args[1] === 'all') {
            for (const [name, info] of Object.entries(MODELS.vosk)) {
                await downloadModel('vosk', name, info);
            }
        } else if (args[1] && MODELS.vosk[args[1]]) {
            await downloadModel('vosk', args[1], MODELS.vosk[args[1]]);
        } else {
            console.error('❌ Invalid Vosk model. Available: en-small, en-large');
        }
    } else {
        console.error('❌ Invalid engine. Use: whisper, vosk, or all');
    }

    console.log('\n🎉 Model download complete!');
    console.log('\n📝 Next steps:');
    console.log('  1. Start your development server: npm run dev');
    console.log('  2. Open the transcription app');
    console.log('  3. Select an offline transcription engine');
    console.log('  4. Start transcribing system audio!');
}

main().catch(console.error); 