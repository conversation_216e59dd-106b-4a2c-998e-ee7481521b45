export interface AudioDevice {
    deviceId: string;
    label: string;
    kind: MediaDeviceKind;
    groupId: string;
}

export interface AudioInputSource {
    id: string;
    name: string;
    type: 'microphone' | 'system' | 'application' | 'loopback' | 'virtual';
    deviceId?: string;
    capabilities?: AudioCapabilities;
}

export interface AudioCapabilities {
    supportsSystemAudio: boolean;
    supportsLoopback: boolean;
    requiresPermission: boolean;
    quality: 'low' | 'medium' | 'high';
}

export interface AudioCaptureOptions {
    sourceType: 'microphone' | 'system' | 'application' | 'loopback' | 'virtual';
    deviceId?: string;
    echoCancellation?: boolean;
    noiseSuppression?: boolean;
    autoGainControl?: boolean;
    sampleRate?: number;
    channelCount?: number;
    bitDepth?: number;
}

export interface TranscriptionEngine {
    name: string;
    type: 'web-speech' | 'whisper' | 'vosk' | 'custom';
    isOnline: boolean;
    supportsRealtime: boolean;
    languages: string[];
}

export interface SystemAudioCapture {
    isSupported: boolean;
    method: 'getDisplayMedia' | 'stereoMix' | 'virtualCable' | 'loopback' | 'none';
    quality: 'low' | 'medium' | 'high';
    latency: number;
}

export interface AudioStreamProcessor {
    process: (audioData: Float32Array) => Promise<string>;
    isProcessing: boolean;
    bufferSize: number;
    sampleRate: number;
}

export interface EnhancedSpeechRecognition extends SpeechRecognition {
    audioContext?: AudioContext;
    mediaStream?: MediaStream;
    sourceNode?: MediaStreamAudioSourceNode;
    processor?: AudioStreamProcessor;
    engine?: TranscriptionEngine;
}